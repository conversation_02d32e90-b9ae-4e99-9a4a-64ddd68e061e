# Système de Gestion d'Images

Ce document décrit le système de gestion d'images de l'application.

## Table des matières

1. [Introduction](#introduction)
2. [Modèle de données](#modèle-de-données)
3. [API pour les images](#api-pour-les-images)
4. [Exemples d'utilisation](#exemples-dutilisation)
5. [Stockage et configuration](#stockage-et-configuration)
6. [Bonnes pratiques](#bonnes-pratiques)

## Introduction

Le système de gestion d'images permet de gérer les images pour différents modèles de l'application (produits, catégories, sous-catégories, sous-sous-catégories, collections, marques, variantes de produits). Il offre des fonctionnalités comme le téléchargement d'images, la création de miniatures, l'optimisation d'images, et la gestion des métadonnées.

### Principales fonctionnalités

- Téléchargement d'images pour différents modèles
- Création automatique de miniatures
- Optimisation d'images
- Gestion des métadonnées (dimensions, taille, type MIME, etc.)
- Support pour les images principales (is_primary)
- Ordonnancement des images
- Accès aux images via un proxy sécurisé

## Modèle de données

### Image

Le modèle `Image` représente une image associée à un modèle de l'application.

```mermaid
classDiagram
    class Image {
        +id: int
        +path: string
        +filename: string
        +disk: string
        +mime_type: string
        +size: int
        +alt_text: string
        +title: string
        +imageable_type: string
        +imageable_id: int
        +is_primary: boolean
        +order: int
        +metadata: json
        +created_at: timestamp
        +updated_at: timestamp
        +deleted_at: timestamp
        +getUrlAttribute()
        +getDirectUrlAttribute()
        +getThumbnailUrl(size)
    }
```

### Relations polymorphiques

Le modèle `Image` utilise une relation polymorphique `imageable` pour s'associer à différents modèles:

```mermaid
classDiagram
    class Image {
        +imageable_type: string
        +imageable_id: int
    }
    
    class Produit {
        +images()
        +getPrimaryImageAttribute()
        +getPrimaryImageUrlAttribute()
    }
    
    class Categorie {
        +images()
        +getPrimaryImageAttribute()
        +getPrimaryImageUrlAttribute()
    }
    
    class SousCategorie {
        +images()
        +getPrimaryImageAttribute()
        +getPrimaryImageUrlAttribute()
    }
    
    class sous_sousCategorie {
        +images()
        +getPrimaryImageAttribute()
        +getPrimaryImageUrlAttribute()
    }
    
    class Collection {
        +images()
        +getPrimaryImageAttribute()
        +getPrimaryImageUrlAttribute()
    }
    
    class Marque {
        +images()
        +getPrimaryImageAttribute()
        +getPrimaryImageUrlAttribute()
    }
    
    class ProduitVariante {
        +images()
        +getPrimaryImageAttribute()
        +getPrimaryImageUrlAttribute()
    }
    
    Image "0..*" -- "1" Produit : imageable
    Image "0..*" -- "1" Categorie : imageable
    Image "0..*" -- "1" SousCategorie : imageable
    Image "0..*" -- "1" sous_sousCategorie : imageable
    Image "0..*" -- "1" Collection : imageable
    Image "0..*" -- "1" Marque : imageable
    Image "0..*" -- "1" ProduitVariante : imageable
```

## API pour les images

### Téléchargement d'images

| Méthode | URL | Description |
|---------|-----|-------------|
| POST | `/api/images/upload` | Télécharger une image |
| POST | `/api/images/upload-multiple` | Télécharger plusieurs images |

#### Télécharger une image

**Endpoint**: `POST /api/images/upload`

**Paramètres**:
- `model_type` (obligatoire): Type de modèle (produit, categorie, sous_categorie, sous_sous_categorie, collection, marque, produit_variante)
- `model_id` (obligatoire): ID du modèle
- `image` (obligatoire): Fichier image
- `is_primary` (optionnel): Définir comme image principale (true/false)
- `alt_text` (optionnel): Texte alternatif
- `title` (optionnel): Titre de l'image

**Réponse**:
```json
{
  "message": "Image uploaded successfully",
  "image": {
    "id": 1,
    "path": "produits/1/image.jpg",
    "filename": "image.jpg",
    "disk": "s3",
    "mime_type": "image/jpeg",
    "size": 123456,
    "alt_text": "Description de l'image",
    "title": "Titre de l'image",
    "imageable_type": "App\\Models\\Produit",
    "imageable_id": 1,
    "is_primary": true,
    "order": 0,
    "metadata": {
      "width": 800,
      "height": 600,
      "original_filename": "image.jpg",
      "extension": "jpg"
    },
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  },
  "url": "https://example.com/api/images/serve/1",
  "direct_url": "https://example.com/api/images/file/produits/1/image.jpg"
}
```

#### Télécharger plusieurs images

**Endpoint**: `POST /api/images/upload-multiple`

**Paramètres**:
- `model_type` (obligatoire): Type de modèle
- `model_id` (obligatoire): ID du modèle
- `images[]` (obligatoire): Tableau de fichiers images
- `is_primary` (optionnel): ID de l'image à définir comme principale
- `alt_text[]` (optionnel): Tableau de textes alternatifs
- `title[]` (optionnel): Tableau de titres

**Réponse**:
```json
{
  "message": "3 images uploaded successfully",
  "images": [
    {
      "id": 1,
      "path": "produits/1/image1.jpg",
      "filename": "image1.jpg",
      "disk": "s3",
      "mime_type": "image/jpeg",
      "size": 123456,
      "alt_text": "Description de l'image 1",
      "title": "Titre de l'image 1",
      "imageable_type": "App\\Models\\Produit",
      "imageable_id": 1,
      "is_primary": true,
      "order": 0,
      "metadata": {
        "width": 800,
        "height": 600,
        "original_filename": "image1.jpg",
        "extension": "jpg"
      },
      "created_at": "2023-01-01T00:00:00.000000Z",
      "updated_at": "2023-01-01T00:00:00.000000Z",
      "url": "https://example.com/api/images/serve/1",
      "direct_url": "https://example.com/api/images/file/produits/1/image1.jpg",
      "thumbnail_small": "https://example.com/api/images/thumbnail/1/small",
      "thumbnail_medium": "https://example.com/api/images/thumbnail/1/medium",
      "thumbnail_large": "https://example.com/api/images/thumbnail/1/large"
    },
    // ... autres images
  ]
}
```

### Récupération d'images

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/images/get` | Obtenir les images d'un modèle |
| GET | `/api/images/serve/{id}` | Servir une image par son ID |
| GET | `/api/images/thumbnail/{id}/{size}` | Servir une miniature d'image |
| GET | `/api/images/file/{path}` | Servir une image par son chemin |

#### Obtenir les images d'un modèle

**Endpoint**: `GET /api/images/get`

**Paramètres**:
- `model_type` (obligatoire): Type de modèle
- `model_id` (obligatoire): ID du modèle

**Réponse**:
```json
{
  "images": [
    {
      "id": 1,
      "path": "produits/1/image1.jpg",
      "filename": "image1.jpg",
      "disk": "s3",
      "mime_type": "image/jpeg",
      "size": 123456,
      "alt_text": "Description de l'image 1",
      "title": "Titre de l'image 1",
      "imageable_type": "App\\Models\\Produit",
      "imageable_id": 1,
      "is_primary": true,
      "order": 0,
      "metadata": {
        "width": 800,
        "height": 600,
        "original_filename": "image1.jpg",
        "extension": "jpg"
      },
      "created_at": "2023-01-01T00:00:00.000000Z",
      "updated_at": "2023-01-01T00:00:00.000000Z",
      "url": "https://example.com/api/images/serve/1",
      "direct_url": "https://example.com/api/images/file/produits/1/image1.jpg",
      "thumbnail_small": "https://example.com/api/images/thumbnail/1/small",
      "thumbnail_medium": "https://example.com/api/images/thumbnail/1/medium",
      "thumbnail_large": "https://example.com/api/images/thumbnail/1/large"
    },
    // ... autres images
  ]
}
```

### Gestion d'images

| Méthode | URL | Description |
|---------|-----|-------------|
| PUT | `/api/images/{id}` | Mettre à jour une image |
| DELETE | `/api/images/{id}` | Supprimer une image |
| POST | `/api/images/reorder` | Réorganiser les images |

#### Mettre à jour une image

**Endpoint**: `PUT /api/images/{id}`

**Paramètres**:
- `is_primary` (optionnel): Définir comme image principale (true/false)
- `alt_text` (optionnel): Texte alternatif
- `title` (optionnel): Titre de l'image
- `order` (optionnel): Ordre d'affichage

**Réponse**:
```json
{
  "message": "Image updated successfully",
  "image": {
    "id": 1,
    "path": "produits/1/image1.jpg",
    "filename": "image1.jpg",
    "disk": "s3",
    "mime_type": "image/jpeg",
    "size": 123456,
    "alt_text": "Nouvelle description",
    "title": "Nouveau titre",
    "imageable_type": "App\\Models\\Produit",
    "imageable_id": 1,
    "is_primary": true,
    "order": 2,
    "metadata": {
      "width": 800,
      "height": 600,
      "original_filename": "image1.jpg",
      "extension": "jpg"
    },
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "url": "https://example.com/api/images/serve/1",
    "direct_url": "https://example.com/api/images/file/produits/1/image1.jpg",
    "thumbnail_small": "https://example.com/api/images/thumbnail/1/small",
    "thumbnail_medium": "https://example.com/api/images/thumbnail/1/medium",
    "thumbnail_large": "https://example.com/api/images/thumbnail/1/large"
  }
}
```

#### Réorganiser les images

**Endpoint**: `POST /api/images/reorder`

**Paramètres**:
- `images` (obligatoire): Tableau d'objets avec `id` et `order`

**Exemple de requête**:
```json
{
  "images": [
    {"id": 1, "order": 2},
    {"id": 2, "order": 1},
    {"id": 3, "order": 0}
  ]
}
```

**Réponse**:
```json
{
  "message": "Images reordered successfully"
}
```

## Exemples d'utilisation

### Télécharger une image pour un produit

```http
POST /api/images/upload
Content-Type: multipart/form-data

model_type=produit
model_id=1
image=@/path/to/image.jpg
is_primary=true
alt_text=Description du produit
title=Image principale du produit
```

### Télécharger une image pour une variante de produit

```http
POST /api/images/upload
Content-Type: multipart/form-data

model_type=produit_variante
model_id=5
image=@/path/to/variant.jpg
is_primary=true
alt_text=Variante rouge taille L
title=Image de la variante
```

### Récupérer les images d'un produit

```http
GET /api/images/get?model_type=produit&model_id=1
```

### Mettre à jour une image

```http
PUT /api/images/1
Content-Type: application/json

{
  "alt_text": "Nouvelle description",
  "title": "Nouveau titre",
  "is_primary": true
}
```

### Supprimer une image

```http
DELETE /api/images/1
```

## Stockage et configuration

Le système d'images utilise le stockage S3 (Cloudflare R2) pour stocker les images. Les images sont servies via un proxy pour éviter les problèmes d'autorisation.

### Configuration

La configuration du stockage S3 est définie dans le fichier `AppServiceProvider.php`:

```php
config([
    'filesystems.default' => 's3',
    'filesystems.disks.s3.driver' => 's3',
    'filesystems.disks.s3.key' => 'your-key',
    'filesystems.disks.s3.secret' => 'your-secret',
    'filesystems.disks.s3.region' => 'auto',
    'filesystems.disks.s3.bucket' => 'your-bucket',
    'filesystems.disks.s3.url' => 'https://your-bucket.r2.cloudflarestorage.com',
    'filesystems.disks.s3.endpoint' => 'https://your-bucket.r2.cloudflarestorage.com',
    'filesystems.disks.s3.use_path_style_endpoint' => true,
    'filesystems.disks.s3.throw' => true,
    'filesystems.disks.s3.visibility' => 'public',
]);
```

### Structure des dossiers

Les images sont stockées dans des dossiers organisés par type de modèle et ID:

- Produits: `produits/{produit_id}/{filename}`
- Catégories: `categories/{categorie_id}/{filename}`
- Sous-catégories: `sous_categories/{sous_categorie_id}/{filename}`
- Sous-sous-catégories: `sous_sous_categories/{sous_sous_categorie_id}/{filename}`
- Collections: `collections/{collection_id}/{filename}`
- Marques: `marques/{marque_id}/{filename}`
- Variantes de produits: `produits/{produit_id}/variantes/{variante_id}/{filename}`

### Miniatures

Les miniatures sont générées automatiquement lors du téléchargement d'une image. Elles sont stockées dans le même dossier que l'image originale, avec un suffixe indiquant la taille:

- Petite: `{filename}_small.{extension}`
- Moyenne: `{filename}_medium.{extension}`
- Grande: `{filename}_large.{extension}`

Les tailles des miniatures sont configurées dans le service `ImageService`:

```php
protected $thumbnailSizes = [
    'small' => [150, 150],
    'medium' => [300, 300],
    'large' => [600, 600],
];
```

## Bonnes pratiques

### Optimisation des images

- Utilisez des formats d'image modernes comme WebP pour réduire la taille des fichiers
- Optimisez les images avant de les télécharger
- Utilisez les miniatures appropriées pour les différentes tailles d'affichage

### Métadonnées

- Fournissez toujours un texte alternatif (alt_text) pour l'accessibilité
- Utilisez des titres descriptifs pour améliorer le référencement

### Performance

- Utilisez un CDN pour servir les images
- Mettez en cache les images côté client
- Utilisez la compression et l'optimisation des images

### Sécurité

- Validez les types de fichiers téléchargés
- Limitez la taille des fichiers téléchargés
- Utilisez le proxy d'images pour contrôler l'accès aux images
