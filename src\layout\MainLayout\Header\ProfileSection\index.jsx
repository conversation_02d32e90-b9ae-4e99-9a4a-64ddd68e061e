import { useRef, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import Transitions from 'ui-component/extended/Transitions';
import useConfig from 'hooks/useConfig';
import { useAuth } from 'contexts/AuthContext';

// assets
import User1 from 'assets/images/users/user-round.svg';
import { IconLogout, IconSettings, IconUser } from '@tabler/icons-react';
import { CircularProgress } from '@mui/material';

// ==============================|| PROFILE MENU ||============================== //

export default function ProfileSection() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { borderRadius } = useConfig();
  const { user, logout, refreshUser } = useAuth();

  const [selectedIndex] = useState(-1);
  const [open, setOpen] = useState(false);
  const [loggingOut, setLoggingOut] = useState(false);

  /**
   * anchorRef is used on different components and specifying one type leads to other components throwing an error
   * */
  const anchorRef = useRef(null);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }

    setOpen(false);
  };

  const handleLogout = async () => {
    try {
      console.log('🔐 User clicked logout button');
      setLoggingOut(true);
      setOpen(false); // Close the menu

      console.log('🔐 Calling logout function...');
      // The logout function will handle the Keycloak redirect
      await logout();

      // Note: We won't reach this point because logout() redirects to Keycloak
      console.log('🔐 Logout completed');
    } catch (error) {
      console.error('❌ Logout failed:', error);
      // If logout fails, still try to redirect to Keycloak login
      console.log('🔐 Redirecting to login page despite logout error');
      navigate('/pages/login');
    } finally {
      setLoggingOut(false);
    }
  };

  const handleProfileClick = () => {
    setOpen(false);
    navigate('/app/profile');
  };

  const prevOpen = useRef(open);
  useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current.focus();
    }

    prevOpen.current = open;
  }, [open]);

  return (
    <>
      <Chip
        sx={{
          ml: 2,
          height: '48px',
          alignItems: 'center',
          borderRadius: '27px',
          '& .MuiChip-label': {
            lineHeight: 0
          }
        }}
        icon={
          <Avatar
            src={User1}
            alt="user-images"
            sx={{
              ...theme.typography.mediumAvatar,
              margin: '8px 0 8px 8px !important',
              cursor: 'pointer'
            }}
            ref={anchorRef}
            aria-controls={open ? 'menu-list-grow' : undefined}
            aria-haspopup="true"
            color="inherit"
          />
        }
        label={<IconSettings stroke={1.5} size="24px" />}
        ref={anchorRef}
        aria-controls={open ? 'menu-list-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        color="primary"
        aria-label="user-account"
      />
      <Popper
        placement="bottom"
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 14]
            }
          }
        ]}
      >
        {({ TransitionProps }) => (
          <ClickAwayListener onClickAway={handleClose}>
            <Transitions in={open} {...TransitionProps}>
              <Paper>
                {open && (
                  <MainCard border={false} elevation={16} content={false} boxShadow shadow={theme.shadows[16]}>
                    <Box sx={{ p: 2, pb: 0 }}>
                      <Stack spacing={1}>
                        {/* User Name */}
                        <Stack direction="row" spacing={0.5} sx={{ alignItems: 'center' }}>
                          <Typography variant="h4">Bienvenue,</Typography>
                          <Typography component="span" variant="h4" sx={{ fontWeight: 400 }}>
                            {user?.name || user?.preferred_username || 'Utilisateur'}
                          </Typography>
                        </Stack>

                        {/* User Email */}
                        {user?.email && (
                          <Typography variant="body2" color="textSecondary">
                            {user.email}
                          </Typography>
                        )}

                        {/* User Role */}
                        {user?.realm_access?.roles && (
                          <Typography variant="caption" color="primary">
                            Administrateur
                          </Typography>
                        )}

                        {/* User Roles */}
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {user?.roles?.map((role, index) => (
                            <Chip
                              key={index}
                              label={role}
                              size="small"
                              color={role === 'admin' ? 'error' : role === 'partenaire' ? 'warning' : 'primary'}
                              variant="outlined"
                            />
                          )) || <Chip label="User" size="small" color="default" variant="outlined" />}
                        </Box>

                        {/* User ID (for debugging) */}
                        {user?.keycloak_id && (
                          <Typography variant="caption" color="textSecondary" sx={{ fontFamily: 'monospace' }}>
                            ID: {user.keycloak_id.substring(0, 8)}...
                          </Typography>
                        )}

                        {/* Data source indicator */}
                        <Typography variant="caption" color="textSecondary" sx={{ fontSize: '0.7rem', fontStyle: 'italic' }}>
                          Données de /api/auth/user
                        </Typography>
                      </Stack>
                      <Divider sx={{ mt: 2 }} />
                    </Box>
                    <Box sx={{ p: 2, py: 1 }}>
                      <List
                        component="nav"
                        sx={{
                          width: '100%',
                          maxWidth: 350,
                          minWidth: 300,
                          borderRadius: `${borderRadius}px`,
                          '& .MuiListItemButton-root': { mt: 0.5 }
                        }}
                      >
                        <ListItemButton
                          sx={{ borderRadius: `${borderRadius}px` }}
                          selected={selectedIndex === 0}
                          onClick={handleProfileClick}
                        >
                          <ListItemIcon>
                            <IconUser stroke={1.5} size="20px" />
                          </ListItemIcon>
                          <ListItemText
                            primary={<Typography variant="body2">Profil</Typography>}
                            secondary={
                              <Typography variant="caption" color="textSecondary">
                                Voir et modifier le profil
                              </Typography>
                            }
                          />
                        </ListItemButton>

                        <Divider sx={{ my: 1 }} />

                        <ListItemButton
                          sx={{ borderRadius: `${borderRadius}px` }}
                          selected={selectedIndex === 4}
                          onClick={handleLogout}
                          disabled={loggingOut}
                        >
                          <ListItemIcon>
                            {loggingOut ? <CircularProgress size={20} /> : <IconLogout stroke={1.5} size="20px" />}
                          </ListItemIcon>
                          <ListItemText
                            primary={<Typography variant="body2">{loggingOut ? 'Déconnexion...' : 'Déconnexion'}</Typography>}
                          />
                        </ListItemButton>
                      </List>
                    </Box>
                  </MainCard>
                )}
              </Paper>
            </Transitions>
          </ClickAwayListener>
        )}
      </Popper>
    </>
  );
}
