import React from 'react';
import { Box, Typography, Button, Paper, Alert } from '@mui/material';

const KeycloakTest = () => {
  const keycloakUrl = process.env.REACT_APP_KEYCLOAK_URL || 'https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud';
  const realm = process.env.REACT_APP_KEYCLOAK_REALM || 'jiheneline';
  const clientId = process.env.REACT_APP_KEYCLOAK_CLIENT_ID || 'backoffice-client';

  const testKeycloakAccess = () => {
    const testUrl = `${keycloakUrl}/realms/${realm}`;
    window.open(testUrl, '_blank');
  };

  const testKeycloakLogin = () => {
    const loginUrl = `${keycloakUrl}/realms/${realm}/protocol/openid-connect/auth`;
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: window.location.origin + '/auth/callback',
      response_type: 'code',
      scope: 'openid profile email'
    });

    const fullUrl = `${loginUrl}?${params.toString()}`;
    console.log('Test URL:', fullUrl);
    window.open(fullUrl, '_blank');
  };

  const directRedirect = () => {
    const loginUrl = `${keycloakUrl}/realms/${realm}/protocol/openid-connect/auth`;
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: window.location.origin + '/auth/callback',
      response_type: 'code',
      scope: 'openid profile email'
    });

    const fullUrl = `${loginUrl}?${params.toString()}`;
    console.log('Direct redirect to:', fullUrl);
    window.location.href = fullUrl;
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Keycloak Configuration Test
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current Configuration
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>Keycloak URL:</strong> {keycloakUrl}
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>Realm:</strong> {realm}
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>Client ID:</strong> {clientId}
        </Typography>
        <Typography variant="body2">
          <strong>Redirect URI:</strong> {window.location.origin}/auth/callback
        </Typography>
      </Paper>

      <Alert severity="info" sx={{ mb: 3 }}>
        Use these buttons to test different aspects of the Keycloak integration.
      </Alert>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Button variant="outlined" onClick={testKeycloakAccess} fullWidth>
          Test 1: Open Keycloak Realm (New Tab)
        </Button>

        <Button variant="outlined" onClick={testKeycloakLogin} fullWidth>
          Test 2: Open Keycloak Login (New Tab)
        </Button>

        <Button variant="contained" color="primary" onClick={directRedirect} fullWidth>
          Test 3: Direct Redirect to Keycloak Login
        </Button>
      </Box>

      <Alert severity="warning" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>Expected Results:</strong>
          <br />
          • Test 1: Should open Keycloak realm info page
          <br />
          • Test 2: Should open Keycloak login page in new tab
          <br />• Test 3: Should redirect current page to Keycloak login
        </Typography>
      </Alert>
    </Box>
  );
};

export default KeycloakTest;
