# Système de Panier

> **Note importante**: Ce système a été mis à jour pour ne plus utiliser les session IDs. Veuillez consulter le document [cart_system_update.md](cart_system_update.md) pour plus d'informations sur les changements récents.

## Introduction

Le système de panier permet aux utilisateurs de sélectionner et de sauvegarder des produits avant de passer une commande. Il est conçu pour fonctionner avec l'authentification Keycloak et utilise des cookies pour maintenir l'état du panier, sans dépendre des sessions Laravel.

## Architecture

Le système de panier est basé sur une architecture cookie-first qui permet :

1. **Persistance sans session** : Le panier est associé à un cookie unique plutôt qu'à une session Laravel
2. **Compatibilité avec Keycloak** : Fonctionne de manière transparente avec l'authentification Keycloak
3. **Fusion de paniers** : Associe automatiquement un panier anonyme à un utilisateur lors de la connexion
4. **Gestion des stocks** : Vérifie la disponibilité des produits lors de l'ajout au panier

## Modèles de données

### Panier

Le modèle `Panier` représente un panier d'achat et peut être associé soit à un utilisateur authentifié, soit à un identifiant de session anonyme.

| Champ       | Type         | Description                                           |
|-------------|--------------|-------------------------------------------------------|
| id          | bigint       | Identifiant unique du panier                          |
| client_id   | bigint       | ID de l'utilisateur (null pour les paniers anonymes)  |
| session_id  | string       | ID de session pour les paniers anonymes               |
| created_at  | timestamp    | Date de création                                      |
| updated_at  | timestamp    | Date de dernière modification                         |

### PanierItem

Le modèle `PanierItem` représente un produit dans le panier avec sa quantité et son prix.

| Champ         | Type         | Description                                        |
|---------------|--------------|--------------------------------------------------- |
| id            | bigint       | Identifiant unique de l'item                       |
| panier_id     | bigint       | ID du panier auquel appartient l'item              |
| produit_id    | bigint       | ID du produit                                      |
| variante_id   | bigint       | ID de la variante du produit (optionnel)           |
| quantite      | integer      | Quantité du produit                                |
| prix_unitaire | decimal      | Prix unitaire du produit au moment de l'ajout      |
| created_at    | timestamp    | Date de création                                   |
| updated_at    | timestamp    | Date de dernière modification                      |

## Endpoints API

### Récupérer le contenu du panier

```http
GET /api/panier
```

Retourne le contenu du panier actuel de l'utilisateur.

#### En-têtes de la requête

| En-tête      | Valeur                | Description                                |
|--------------|----------------------|--------------------------------------------|
| Cookie       | cart_session=<uuid>   | Cookie d'identification du panier (optionnel) |
| Cookie       | access_token=<token>  | Token d'authentification Keycloak (optionnel) |

#### Exemple de réponse

```json
{
  "panier": {
    "id": 1,
    "items": [
      {
        "id": 1,
        "produit": {
          "id": 5,
          "nom": "Smartphone XYZ",
          "reference": "2-5",
          "image": "smartphone_xyz.jpg"
        },
        "variante": {
          "id": 3,
          "sku": "XYZ-RED-128",
          "attributs": [
            {
              "nom": "Couleur",
              "valeur": "Rouge"
            },
            {
              "nom": "Stockage",
              "valeur": "128 Go"
            }
          ]
        },
        "quantite": 1,
        "prix_unitaire": 599.99,
        "sous_total": 599.99
      },
      {
        "id": 2,
        "produit": {
          "id": 8,
          "nom": "Écouteurs sans fil",
          "reference": "3-8",
          "image": "ecouteurs.jpg"
        },
        "variante": null,
        "quantite": 2,
        "prix_unitaire": 79.99,
        "sous_total": 159.98
      }
    ],
    "total": 759.97
  }
}
```

### Ajouter un produit au panier

```http
POST /api/panier/ajouter
```

Ajoute un produit au panier.

#### Paramètres de la requête

| Paramètre    | Type    | Description                                |
|--------------|---------|--------------------------------------------|
| produit_id   | integer | ID du produit à ajouter                    |
| variante_id  | integer | ID de la variante (optionnel)              |
| quantite     | integer | Quantité à ajouter (minimum 1)             |

#### Exemple de requête

```json
{
  "produit_id": 5,
  "variante_id": 3,
  "quantite": 1
}
```

#### Exemple de réponse

Même format que pour `GET /api/panier` avec le produit ajouté.

### Mettre à jour la quantité d'un item

```http
PUT /api/panier/items/{itemId}
```

Met à jour la quantité d'un item dans le panier.

#### Paramètres de la requête

| Paramètre    | Type    | Description                                |
|--------------|---------|--------------------------------------------|
| quantite     | integer | Nouvelle quantité (0 pour supprimer)       |

#### Exemple de requête

```json
{
  "quantite": 3
}
```

#### Exemple de réponse

Même format que pour `GET /api/panier` avec la quantité mise à jour.

### Supprimer un item du panier

```http
DELETE /api/panier/items/{itemId}
```

Supprime un item du panier.

#### Exemple de réponse

Même format que pour `GET /api/panier` sans l'item supprimé.

### Vider le panier

```http
DELETE /api/panier/vider
```

Supprime tous les items du panier.

#### Exemple de réponse

```json
{
  "message": "Panier vidé avec succès"
}
```

## Gestion des cookies

Le système utilise un cookie nommé `cart_session` pour identifier le panier de l'utilisateur. Ce cookie :

1. Est créé automatiquement lors de la première interaction avec le panier
2. A une durée de vie de 30 jours
3. Est sécurisé (HTTPS uniquement)
4. Est HttpOnly pour empêcher l'accès via JavaScript
5. A l'attribut SameSite=Lax pour la sécurité CSRF

## Fusion des paniers

Lorsqu'un utilisateur anonyme se connecte, le système fusionne automatiquement le panier anonyme avec le panier de l'utilisateur authentifié :

1. Si l'utilisateur n'a pas de panier existant, le panier anonyme est simplement associé à l'utilisateur
2. Si l'utilisateur a déjà un panier, les items du panier anonyme sont ajoutés au panier de l'utilisateur
3. En cas de conflit (même produit/variante), les quantités sont additionnées
4. Le panier anonyme est supprimé après la fusion

## Vérification des stocks

Le système vérifie automatiquement la disponibilité des stocks :

1. Lors de l'ajout d'un produit au panier
2. Lors de la mise à jour de la quantité d'un item

Si la quantité demandée dépasse le stock disponible, une erreur est retournée.

## Intégration avec Keycloak

Le système de panier s'intègre avec Keycloak :

1. Pour les utilisateurs non authentifiés, le panier est associé au cookie `cart_session`
2. Pour les utilisateurs authentifiés, le panier est associé à leur ID utilisateur
3. Lors de l'authentification, le middleware `VerifyKeycloakToken` extrait l'ID utilisateur du token JWT
4. Le contrôleur de panier utilise cet ID pour associer le panier à l'utilisateur

## Exemples d'utilisation

### Ajouter un produit au panier (JavaScript)

```javascript
async function ajouterAuPanier(produitId, quantite = 1, varianteId = null) {
  try {
    const response = await fetch('/api/panier/ajouter', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        produit_id: produitId,
        quantite: quantite,
        variante_id: varianteId
      }),
      credentials: 'include' // Important pour inclure les cookies
    });

    if (!response.ok) {
      throw new Error('Erreur lors de l\'ajout au panier');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erreur:', error);
    throw error;
  }
}
```

### Récupérer le contenu du panier (JavaScript)

```javascript
async function recupererPanier() {
  try {
    const response = await fetch('/api/panier', {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      },
      credentials: 'include' // Important pour inclure les cookies
    });

    if (!response.ok) {
      throw new Error('Erreur lors de la récupération du panier');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erreur:', error);
    throw error;
  }
}
```

## Notes techniques

- Le système n'utilise pas les sessions Laravel, ce qui le rend adapté aux API sans état
- Les cookies sont utilisés pour maintenir l'état du panier entre les requêtes
- Le middleware `CookieCartMiddleware` gère la création et la maintenance des cookies de panier
- Le système est compatible avec les applications SPA et les applications mobiles qui peuvent stocker le cookie de session
