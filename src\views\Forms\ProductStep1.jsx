import React from 'react';
import { Form, Row, Col } from 'react-bootstrap';

const ProductStep1 = ({ formData, setFormData, error }) => (
  <>
    <Row>
      <Col md={6}>
        <Form.Group controlId="nom_produit">
          <Form.Label>Nom du produit</Form.Label>
          <Form.Control
            type="text"
            value={formData.nom_produit || ''}
            onChange={(e) => setFormData((prev) => ({ ...prev, nom_produit: e.target.value }))}
            required
            isInvalid={!!error && !formData.nom_produit}
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group controlId="reference">
          <Form.Label>Référence</Form.Label>
          <Form.Control
            type="text"
            value={formData.reference || ''}
            onChange={(e) => setFormData((prev) => ({ ...prev, reference: e.target.value }))}
          />
        </Form.Group>
      </Col>
    </Row>
    <Row>
      <Col md={12}>
        <Form.Group controlId="description_produit">
          <Form.Label>Description</Form.Label>
          <Form.Control
            as="textarea"
            value={formData.description_produit || ''}
            onChange={(e) => setFormData((prev) => ({ ...prev, description_produit: e.target.value }))}
            required
            isInvalid={!!error && !formData.description_produit}
          />
        </Form.Group>
      </Col>
    </Row>
    <Row>
      <Col md={6}>
        <Form.Group controlId="prix_produit">
          <Form.Label>Prix</Form.Label>
          <Form.Control
            type="number"
            value={formData.prix_produit || ''}
            onChange={(e) => setFormData((prev) => ({ ...prev, prix_produit: e.target.value }))}
            required
            isInvalid={!!error && !formData.prix_produit}
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group controlId="quantite_produit">
          <Form.Label>Quantité</Form.Label>
          <Form.Control
            type="number"
            value={formData.quantite_produit || 0}
            onChange={(e) => setFormData((prev) => ({ ...prev, quantite_produit: e.target.value }))}
          />
        </Form.Group>
      </Col>
    </Row>
  </>
);

export default ProductStep1;
