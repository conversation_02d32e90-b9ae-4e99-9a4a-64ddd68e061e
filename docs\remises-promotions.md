# Système de Remises et Promotions

## Introduction

Le système de remises et promotions permet de gérer différentes réductions de prix pour les produits en fonction de plusieurs critères :

- Profil de remise du client (standard, premium, affilié, groupe)
- Remises personnelles par client
- Promotions sur des produits spécifiques
- Promotions sur des collections de produits

## Concepts clés

### Profils de remise

Les profils de remise définissent les types de clients et leurs avantages par défaut :

| Profil de remise | Description                                                |
|------------------|------------------------------------------------------------|
| standard         | Client standard sans remise spécifique par défaut          |
| premium          | Client bénéficiant d'une remise partenaire                 |
| affilie          | Client associé à un point de vente physique                |
| groupe           | Client appartenant à un groupe avec une remise commune     |

### Règles de remise

Les règles de remise définissent les remises par défaut pour chaque profil client :

- Type de remise (pourcentage ou montant fixe)
- Valeur de la remise
- Priorité (pour gérer les conflits entre règles)
- Conditions supplémentaires (optionnelles)

### Promotions

Les promotions sont des réductions temporaires qui peuvent être appliquées à des produits ou des collections :

- Type de promotion (pourcentage, montant fixe, gratuit)
- Valeur de la promotion
- Période de validité (dates de début et de fin)
- Cumulabilité avec d'autres promotions
- Restrictions par profil client (optionnelles)

## API Endpoints

### Gestion des règles de remise

```http
GET /api/regle-remises
```

Récupère la liste des règles de remise avec possibilité de filtrage par type de client et statut.

```http
GET /api/regle-remises/{id}
```

Récupère les détails d'une règle de remise spécifique.

```http
POST /api/regle-remises
```

Crée une nouvelle règle de remise.

**Paramètres :**

| Nom                        | Type    | Description                                      |
|----------------------------|---------|--------------------------------------------------|
| nom                        | string  | Nom de la règle de remise                        |
| description                | string  | Description détaillée (optionnelle)              |
| type_client                | string  | Type de client (standard, premium, affilie, groupe) |
| valeur                     | decimal | Valeur de la remise                              |
| type                       | string  | Type de remise (pourcentage, montant_fixe)       |
| priorité                   | integer | Priorité de la règle (optionnelle)               |
| active                     | boolean | Statut d'activation (optionnel)                  |
| conditions_supplementaires | array   | Conditions supplémentaires (optionnelles)        |

```http
PUT /api/regle-remises/{id}
```

Met à jour une règle de remise existante.

```http
DELETE /api/regle-remises/{id}
```

Supprime une règle de remise.

### Gestion des promotions

```http
GET /api/promotions
```

Récupère la liste des promotions avec possibilité de filtrage par statut, type et dates.

```http
GET /api/promotions/{id}
```

Récupère les détails d'une promotion spécifique avec ses relations (produits, collections, profils de remise).

```http
POST /api/promotions
```

Crée une nouvelle promotion.

**Paramètres :**

| Nom         | Type    | Description                                           |
|-------------|---------|-------------------------------------------------------|
| nom         | string  | Nom de la promotion                                   |
| code        | string  | Code unique (optionnel)                               |
| description | string  | Description détaillée (optionnelle)                   |
| type        | string  | Type de promotion (pourcentage, montant_fixe, gratuit)|
| valeur      | decimal | Valeur de la promotion                                |
| statut      | string  | Statut (active, inactive, programmée)                 |
| date_debut  | date    | Date de début (optionnelle)                           |
| date_fin    | date    | Date de fin (optionnelle)                             |
| priorité    | integer | Priorité de la promotion (optionnelle)                |
| cumulable   | boolean | Si la promotion est cumulable avec d'autres (optionnel)|
| conditions  | array   | Conditions supplémentaires (optionnelles)             |
| produits    | array   | IDs des produits à associer (optionnel)               |
| collections | array   | IDs des collections à associer (optionnel)            |
| profils_remise | array | Profils de remise autorisés (optionnel)              |

```http
PUT /api/promotions/{id}
```

Met à jour une promotion existante.

```http
DELETE /api/promotions/{id}
```

Supprime une promotion.

### Association de promotions

```http
POST /api/produits/{produit}/promotions
```

Associe une promotion à un produit.

**Paramètres :**

| Nom          | Type    | Description                                |
|--------------|---------|-------------------------------------------|
| promotion_id | integer | ID de la promotion à associer              |
| date_debut   | date    | Date de début spécifique (optionnelle)     |
| date_fin     | date    | Date de fin spécifique (optionnelle)       |

```http
DELETE /api/produits/{produit}/promotions/{promotion}
```

Détache une promotion d'un produit.

```http
POST /api/collections/{collection}/promotions
```

Associe une promotion à une collection.

**Paramètres :**

| Nom          | Type    | Description                                |
|--------------|---------|-------------------------------------------|
| promotion_id | integer | ID de la promotion à associer              |
| date_debut   | date    | Date de début spécifique (optionnelle)     |
| date_fin     | date    | Date de fin spécifique (optionnelle)       |

```http
DELETE /api/collections/{collection}/promotions/{promotion}
```

Détache une promotion d'une collection.

## Logique de calcul des remises

Le système applique les remises dans l'ordre suivant :

1. **Remise basée sur le profil client** : Appliquée en fonction du profil de remise du client (standard, premium, affilié, groupe).

2. **Remise personnelle du client** : Remise spécifique attribuée individuellement au client.

3. **Promotions sur le produit** : Promotions directement associées au produit.

4. **Promotions sur les collections** : Promotions associées aux collections dont le produit fait partie.

Les règles de cumul sont les suivantes :

- Les remises basées sur le profil client et les remises personnelles sont toujours cumulées.
- Les promotions marquées comme "cumulables" peuvent s'ajouter à d'autres promotions.
- Les promotions non cumulables remplacent les autres promotions si elles offrent une remise plus avantageuse.
- La remise totale est plafonnée au prix du produit (pas de prix négatif).

### Gestion des dates de validité

Les promotions peuvent avoir des dates de validité à deux niveaux :

1. **Niveau de la promotion** : Chaque promotion peut avoir une date de début (`date_debut`) et une date de fin (`date_fin`) globales.

2. **Niveau de l'association** : Lorsqu'une promotion est associée à un produit ou une collection, cette association peut également avoir ses propres dates de début et de fin, permettant de personnaliser la période de validité pour chaque produit ou collection.

Le système vérifie les deux niveaux de dates pour déterminer si une promotion est active pour un produit donné :

```php
// Exemple de code pour vérifier si une promotion est active
$now = now();
$query->whereHas('promotions', function ($q) use ($now) {
    // Vérifier le statut et les dates au niveau de la promotion
    $q->where('promotions.statut', 'active')
        ->where(function ($q) use ($now) {
            $q->whereNull('promotions.date_debut')
                ->orWhere('promotions.date_debut', '<=', $now);
        })
        ->where(function ($q) use ($now) {
            $q->whereNull('promotions.date_fin')
                ->orWhere('promotions.date_fin', '>=', $now);
        })
        // Vérifier les dates au niveau de l'association produit-promotion
        ->where(function ($q) use ($now) {
            $q->whereNull('produit_promotion.date_debut')
                ->orWhere('produit_promotion.date_debut', '<=', $now);
        })
        ->where(function ($q) use ($now) {
            $q->whereNull('produit_promotion.date_fin')
                ->orWhere('produit_promotion.date_fin', '>=', $now);
        });
});
```

### Filtrage des produits en promotion

L'API permet de filtrer les produits qui sont actuellement en promotion en utilisant le paramètre `en_promotion=true` :

```http
GET /api/produits?en_promotion=true
```

Ce filtre vérifie :

1. Que le produit est associé à au moins une promotion active
2. Que la promotion est dans sa période de validité (au niveau de la promotion et de l'association)

**Note importante** : Pour éviter les ambiguïtés de colonnes dans les requêtes SQL, les noms des colonnes sont qualifiés avec les noms des tables (`promotions.date_debut`, `produit_promotion.date_debut`, etc.).

## Exemples d'utilisation

### Création d'une règle de remise pour les clients premium

```json
{
  "nom": "Remise clients premium",
  "description": "Remise standard pour tous les clients premium",
  "type_client": "premium",
  "valeur": 10,
  "type": "pourcentage",
  "priorité": 10,
  "active": true
}
```

### Création d'une promotion pour une collection

```json
{
  "nom": "Soldes d'été",
  "description": "Promotion sur toute la collection été",
  "type": "pourcentage",
  "valeur": 20,
  "statut": "programmée",
  "date_debut": "2025-06-01",
  "date_fin": "2025-08-31",
  "priorité": 5,
  "cumulable": false,
  "collections": [3, 5, 8],
  "profils_remise": ["standard", "premium"]
}
```

### Association d'une promotion à un produit

```json
{
  "promotion_id": 12,
  "date_debut": "2025-05-01",
  "date_fin": "2025-05-15"
}
```

### Ajout de produits à une collection

Lorsque vous ajoutez des produits à une collection, vous devez utiliser le format suivant :

```json
{
  "produits": [
    {
      "id": 1,
      "ordre": 1,
      "featured": true
    },
    {
      "id": 2,
      "ordre": 2,
      "featured": false
    }
  ]
}
```

Chaque produit dans le tableau `produits` doit avoir :

- `id` : L'identifiant du produit (obligatoire)
- `ordre` : L'ordre d'affichage dans la collection (optionnel)
- `featured` : Si le produit est mis en avant dans la collection (optionnel)

## Bonnes pratiques

1. **Priorités** : Utilisez les priorités pour contrôler l'ordre d'application des remises et promotions.

2. **Dates** : Définissez toujours des dates de début et de fin pour les promotions temporaires.

3. **Profils de remise** : Limitez les promotions à certains profils de remise pour créer des offres exclusives.

4. **Cumulabilité** : Utilisez avec précaution la cumulabilité des promotions pour éviter des remises excessives.

5. **Tests** : Testez les combinaisons de remises et promotions pour vous assurer que le calcul final est correct.
