# Gestion des Catégories

## Introduction

Le système permet de gérer une hiérarchie de catégories et sous-catégories pour organiser les produits.

## Endpoints API

### Catégories

#### Récupérer toutes les catégories

```
GET /api/categories
```

Retourne la liste de toutes les catégories.

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "nom": "Informatique",
    "description": "Produits informatiques et accessoires",
    "image": "informatique.jpg",
    "created_at": "2025-03-10T09:00:00.000000Z",
    "updated_at": "2025-03-10T09:00:00.000000Z"
  },
  {
    "id": 2,
    "nom": "Téléphonie",
    "description": "Smartphones et accessoires",
    "image": "telephonie.jpg",
    "created_at": "2025-03-10T09:05:00.000000Z",
    "updated_at": "2025-03-10T09:05:00.000000Z"
  }
]
```

#### Récupérer une catégorie spécifique

```
GET /api/categories/{id}
```

Retourne les détails d'une catégorie spécifique.

#### Exemple de réponse

```json
{
  "id": 1,
  "nom": "Informatique",
  "description": "Produits informatiques et accessoires",
  "image": "informatique.jpg",
  "created_at": "2025-03-10T09:00:00.000000Z",
  "updated_at": "2025-03-10T09:00:00.000000Z"
}
```

#### Créer une nouvelle catégorie

```
POST /api/categories
```

Crée une nouvelle catégorie.

#### Paramètres de la requête

| Paramètre   | Type   | Description                    |
|-------------|--------|--------------------------------|
| nom         | string | Nom de la catégorie            |
| description | string | Description de la catégorie    |
| image       | string | Nom du fichier image           |

#### Exemple de requête

```json
{
  "nom": "Électroménager",
  "description": "Appareils électroménagers pour la maison",
  "image": "electromenager.jpg"
}
```

#### Mettre à jour une catégorie

```
PUT /api/categories/{id}
```

Met à jour les informations d'une catégorie existante.

#### Paramètres de la requête

Mêmes paramètres que pour la création, tous optionnels.

#### Supprimer une catégorie

```
DELETE /api/categories/{id}
```

Supprime une catégorie existante.

### Sous-catégories

#### Récupérer toutes les sous-catégories

```
GET /api/sousCategories
```

Retourne la liste de toutes les sous-catégories.

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "nom": "Ordinateurs de bureau",
    "description": "PC fixes et tout-en-un",
    "categorie_id": 1,
    "created_at": "2025-03-10T10:00:00.000000Z",
    "updated_at": "2025-03-10T10:00:00.000000Z",
    "categorie": {
      "id": 1,
      "nom": "Informatique"
    }
  },
  {
    "id": 2,
    "nom": "Ordinateurs portables",
    "description": "Laptops et ultrabooks",
    "categorie_id": 1,
    "created_at": "2025-03-10T10:05:00.000000Z",
    "updated_at": "2025-03-10T10:05:00.000000Z",
    "categorie": {
      "id": 1,
      "nom": "Informatique"
    }
  }
]
```

#### Récupérer une sous-catégorie spécifique

```
GET /api/sousCategories/{id}
```

Retourne les détails d'une sous-catégorie spécifique.

#### Récupérer les sous-catégories d'une catégorie

```
GET /api/categories/{id}/sousCategories
```

Retourne toutes les sous-catégories appartenant à une catégorie spécifique.

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "nom": "Ordinateurs de bureau",
    "description": "PC fixes et tout-en-un",
    "categorie_id": 1,
    "created_at": "2025-03-10T10:00:00.000000Z",
    "updated_at": "2025-03-10T10:00:00.000000Z"
  },
  {
    "id": 2,
    "nom": "Ordinateurs portables",
    "description": "Laptops et ultrabooks",
    "categorie_id": 1,
    "created_at": "2025-03-10T10:05:00.000000Z",
    "updated_at": "2025-03-10T10:05:00.000000Z"
  }
]
```

#### Créer une nouvelle sous-catégorie

```
POST /api/sousCategories
```

Crée une nouvelle sous-catégorie.

#### Paramètres de la requête

| Paramètre   | Type    | Description                     |
|-------------|---------|----------------------------------|
| nom         | string  | Nom de la sous-catégorie         |
| description | string  | Description de la sous-catégorie |
| categorie_id| integer | ID de la catégorie parente       |

#### Exemple de requête

```json
{
  "nom": "Tablettes",
  "description": "Tablettes tactiles",
  "categorie_id": 1
}
```

#### Mettre à jour une sous-catégorie

```
PUT /api/sousCategories/{id}
```

Met à jour les informations d'une sous-catégorie existante.

#### Paramètres de la requête

Mêmes paramètres que pour la création, tous optionnels.

#### Supprimer une sous-catégorie

```
DELETE /api/sousCategories/{id}
```

Supprime une sous-catégorie existante.

## Caractéristiques par catégorie

Le système permet également de définir quelles caractéristiques sont applicables à chaque catégorie.

### Récupérer les caractéristiques d'une catégorie

```
GET /api/categories/{id}/caracteristiques
```

Retourne toutes les caractéristiques associées à une catégorie.

### Associer une caractéristique à une catégorie

```
POST /api/categories/{categorie_id}/caracteristiques
```

#### Paramètres de la requête

```json
{
  "caracteristique_id": 1
}
```

### Supprimer une caractéristique d'une catégorie

```
DELETE /api/categories/{categorie_id}/caracteristiques/{caracteristique_id}
```

## Caractéristiques par sous-catégorie

De même, le système permet de définir des caractéristiques spécifiques pour chaque sous-catégorie.

### Récupérer les caractéristiques d'une sous-catégorie

```
GET /api/sousCategories/{id}/caracteristiques
```

### Associer une caractéristique à une sous-catégorie

```
POST /api/sousCategories/{sousCategorie_id}/caracteristiques
```

### Supprimer une caractéristique d'une sous-catégorie

```
DELETE /api/sousCategories/{sousCategorie_id}/caracteristiques/{caracteristique_id}
```
