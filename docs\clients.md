# Gestion des Clients

## Introduction

Le système permet de gérer différents types de clients avec des structures de remises personnalisées. Les clients sont initialement créés via Keycloak et synchronisés avec la base de donn<PERSON>.

## Intégration avec Keycloak

Le système est intégré avec Keycloak pour la gestion des identités et des rôles :

1. **Authentification** : Les utilisateurs s'authentifient via Keycloak
2. **Gestion des rôles** : Les rôles sont définis et gérés dans Keycloak
3. **Synchronisation** : Les rôles et les informations utilisateur sont synchronisés entre Keycloak et l'application

### Clients Keycloak

Le système utilise trois clients Keycloak :

1. **api-client** : Utilisé par le backend pour l'authentification et l'autorisation
2. **backoffice-client** : Utilisé par l'interface d'administration
3. **frontoffice-client** : Utilisé par l'interface client

## Rôles et profils de remise

Le système utilise deux concepts distincts pour gérer les clients :

1. **Rôles utilisateur** : Définis dans Keycloak, ils contrôlent l'accès aux fonctionnalités (ex: 'client', 'partenaire', 'admin')
2. **Profils de remise** : Définis dans l'application, ils déterminent la logique métier et les remises applicables

Tous les utilisateurs affichés dans l'API des clients ont au minimum le rôle 'client'. Le système maintient automatiquement la cohérence entre les rôles Keycloak et les profils de remise (ex: un utilisateur avec le profil 'premium' aura automatiquement le rôle 'partenaire' dans Keycloak).

## Profils de remise

Le système prend en charge quatre profils de remise:

1. **Standard** (anciennement "normal"): Client standard sans remise spécifique
2. **Premium** (anciennement "partenaire"): Client bénéficiant d'une remise partenaire
3. **Affilié** (anciennement "point_de_vente"): Client associé à un point de vente physique
4. **Groupe**: Client appartenant à un groupe avec une remise commune

> **Note**: Pour assurer la compatibilité avec les systèmes existants, l'API continue de supporter les anciens types de clients ('normal', 'partenaire', 'point_de_vente', 'groupe') tout en introduisant les nouveaux profils de remise ('standard', 'premium', 'affilie', 'groupe').

## Endpoints API

### Récupérer tous les clients

```http
GET /api/clients
```

Retourne la liste de tous les clients.

#### Paramètres de filtrage (optionnels)

| Paramètre        | Type   | Description                                                |
|------------------|--------|------------------------------------------------------------|
| type             | string | Filtrer par type de client (normal, partenaire, point_de_vente, groupe) |
| point_de_vente_id| integer| Filtrer par ID de point de vente                           |
| groupe_client_id | integer| Filtrer par ID de groupe de clients                        |

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "name": "Youssef Mrabet",
    "email": "<EMAIL>",
    "roles": ["client"],
    "point_de_vente_id": null,
    "groupe_client_id": null,
    "remise_personnelle": 10.50,
    "type_client": "normal",
    "remise_effective": 10.5
  },
  {
    "id": 2,
    "name": "Client Partenaire",
    "email": "<EMAIL>",
    "roles": ["client", "partenaire"],
    "point_de_vente_id": null,
    "groupe_client_id": null,
    "remise_personnelle": 0,
    "profil_remise": "premium",
    "remise_effective": 15.0,
    "partenaire": {
      "id": 1,
      "remise": 15.0,
      "description": "Partenaire premium",
      "statut": "actif"
    }
  }
]
```

### Récupérer un client spécifique

```http
GET /api/clients/{id}
```

Retourne les détails d'un client spécifique.

#### Exemple de réponse

```json
{
  "id": 1,
  "name": "Youssef Mrabet",
  "email": "<EMAIL>",
  "roles": ["client"],
  "point_de_vente_id": null,
  "groupe_client_id": null,
  "remise_personnelle": 10.50,
  "type_client": "normal",
  "profil_remise": "standard",
  "remise_effective": 10.5
}
```

### Récupérer la dernière commande d'un client

```http
GET /api/clients/{id}/derniere-commande
```

Retourne la dernière commande effectuée par un client.

#### Exemple de réponse

```json
{
  "id": 5,
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "total_commande": 450.75,
  "remise_commande": 10.50,
  "created_at": "2025-04-03T14:25:30.000000Z",
  "updated_at": "2025-04-03T14:25:30.000000Z",
  "produits": [
    {
      "id": 2,
      "nom": "Smartphone XYZ",
      "prix": 499.99,
      "pivot": {
        "commande_id": 5,
        "produit_id": 2,
        "quantite": 1,
        "prix_unitaire": 499.99
      }
    }
  ],
  "client_remise": 10.5
}
```

### Récupérer toutes les commandes d'un client

```http
GET /api/clients/{id}/commandes
```

Retourne toutes les commandes effectuées par un client.

#### Exemple de réponse

```json
[
  {
    "id": 5,
    "user_id": 1,
    "total_commande": 450.75,
    "remise_commande": 10.50,
    "created_at": "2025-04-03T14:25:30.000000Z"
  },
  {
    "id": 3,
    "user_id": 1,
    "total_commande": 120.00,
    "remise_commande": 10.50,
    "created_at": "2025-04-01T10:15:22.000000Z"
  }
]
```

### Mettre à jour la remise personnelle d'un client

```http
PUT /api/clients/{id}/remise
```

Met à jour la remise personnelle d'un client.

#### Paramètres de la requête

| Paramètre         | Type   | Description                                |
|-------------------|--------|--------------------------------------------|
| remise_personnelle| decimal| Pourcentage de remise (0-100)              |

#### Exemple de requête

```json
{
  "remise_personnelle": 12.75
}
```

#### Exemple de réponse

```json
{
  "id": 1,
  "name": "Youssef Mrabet",
  "email": "<EMAIL>",
  "remise_personnelle": 12.75,
  "profil_remise": "standard",
  "remise_effective": 12.75
}
```

### Mettre à jour le profil de remise d'un client

```http
PUT /api/clients/{id}/profil-remise
```

Met à jour le profil de remise d'un client et ses associations. Le système maintient automatiquement la cohérence entre le profil de remise et les rôles utilisateur.

#### Paramètres de la requête

| Paramètre        | Type   | Description                                                |
|------------------|--------|------------------------------------------------------------|
| profil_remise    | string | Profil de remise (standard, premium, affilie, groupe)|
| point_de_vente_id| integer| ID du point de vente (requis si profil_remise=affilie)|
| groupe_client_id | integer| ID du groupe (requis si profil_remise=groupe)                |

#### Exemple de requête

```json
{
  "type_client": "groupe",
  "groupe_client_id": 2
}
```

#### Exemple de réponse

```json
{
  "id": 1,
  "name": "Youssef Mrabet",
  "email": "<EMAIL>",
  "roles": ["client"],
  "point_de_vente_id": null,
  "groupe_client_id": 2,
  "remise_personnelle": 12.75,
  "profil_remise": "groupe",
  "remise_effective": 12.75,
  "groupe_client": {
    "id": 2,
    "nom": "Clients Premium",
    "remise": 8.50,
    "description": "Groupe de clients premium",
    "statut": "actif"
  }
}
```

## Logique de remise

Le système applique la remise la plus avantageuse pour le client selon la hiérarchie suivante:

1. **Remise personnelle**: Si définie, elle prend priorité sur toutes les autres remises
2. **Remise selon le type de client**:
   - Pour les partenaires: remise définie au niveau du partenaire
   - Pour les points de vente: remise définie au niveau du point de vente
   - Pour les groupes: remise définie au niveau du groupe
   - Pour les clients normaux: pas de remise par défaut

## Endpoint administrateur

Les administrateurs peuvent accéder à un endpoint spécial pour récupérer la dernière commande d'un client:

```http
GET /api/v1/admin/clients/{id}/derniere-commande
```

Cet endpoint nécessite l'authentification avec un compte administrateur.
