# Gestion des Images

Ce document décrit le système de gestion des images pour l'API Laravel. Le système permet de gérer les images pour différents types de modèles (produits, catégories, collections, etc.) avec des fonctionnalités d'optimisation et de redimensionnement.

## Architecture

Le système de gestion des images est composé des éléments suivants :

1. **Modèle Image** : Représente une image dans la base de données avec des métadonnées associées.
2. **Service ImageService** : Gère le téléchargement, l'optimisation et la création de miniatures.
3. **Contrôleur ImageController** : Expose les endpoints API pour la gestion des images.
4. **Relations polymorphiques** : Permettent d'associer des images à différents types de modèles.

## Configuration S3

Le système utilise un bucket S3 pour stocker les images. La configuration se fait dans le fichier `.env` :

```
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=your_region
AWS_BUCKET=your_bucket_name
AWS_URL=https://your-bucket-url.s3.your-region.amazonaws.com
AWS_ENDPOINT=https://s3.your-region.amazonaws.com
AWS_USE_PATH_STYLE_ENDPOINT=false
```

## Modèle Image

Le modèle `Image` contient les champs suivants :

| Champ | Type | Description |
|-------|------|-------------|
| id | int | Identifiant unique |
| path | string | Chemin de l'image dans S3 |
| filename | string | Nom de fichier original |
| disk | string | Disque de stockage (par défaut: s3) |
| mime_type | string | Type MIME de l'image |
| size | int | Taille en octets |
| alt_text | string | Texte alternatif pour l'accessibilité |
| title | string | Titre de l'image |
| imageable_type | string | Type du modèle associé |
| imageable_id | int | ID du modèle associé |
| is_primary | boolean | Indique si c'est l'image principale |
| order | int | Ordre d'affichage |
| metadata | json | Métadonnées supplémentaires (dimensions, etc.) |

## Endpoints API

### Télécharger une image

```
POST /api/images/upload
```

Télécharge une image et l'associe à un modèle.

#### Paramètres de la requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| model_type | string | Type de modèle (produit, categorie, sous_categorie, sous_sous_categorie, collection, marque, produit_variante) |
| model_id | int | ID du modèle |
| image | file | Fichier image à télécharger (max: 10MB) |
| is_primary | boolean | Indique si c'est l'image principale (optionnel) |
| alt_text | string | Texte alternatif (optionnel) |
| title | string | Titre de l'image (optionnel) |

#### Exemple de réponse

```json
{
  "message": "Image uploaded successfully",
  "image": {
    "id": 1,
    "path": "produits/1/product_abc123.jpg",
    "filename": "product.jpg",
    "disk": "s3",
    "mime_type": "image/jpeg",
    "size": 102400,
    "alt_text": "Description du produit",
    "title": "Image principale du produit",
    "imageable_type": "App\\Models\\Produit",
    "imageable_id": 1,
    "is_primary": true,
    "order": 0,
    "metadata": {
      "width": 800,
      "height": 600,
      "original_filename": "product.jpg",
      "extension": "jpg"
    },
    "created_at": "2025-04-10T15:30:00.000000Z",
    "updated_at": "2025-04-10T15:30:00.000000Z"
  },
  "url": "https://your-bucket.s3.your-region.amazonaws.com/produits/1/product_abc123.jpg"
}
```

### Télécharger plusieurs images

```
POST /api/images/upload-multiple
```

Télécharge plusieurs images et les associe à un modèle.

#### Paramètres de la requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| model_type | string | Type de modèle |
| model_id | int | ID du modèle |
| images[] | file[] | Fichiers images à télécharger |
| alt_text | string | Texte alternatif (optionnel) |
| title | string | Titre des images (optionnel) |

### Récupérer les images d'un modèle

```
GET /api/images/get
```

Récupère toutes les images associées à un modèle.

#### Paramètres de la requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| model_type | string | Type de modèle |
| model_id | int | ID du modèle |

### Mettre à jour une image

```
PUT /api/images/{id}
```

Met à jour les détails d'une image.

#### Paramètres de la requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| is_primary | boolean | Indique si c'est l'image principale (optionnel) |
| alt_text | string | Texte alternatif (optionnel) |
| title | string | Titre de l'image (optionnel) |
| order | int | Ordre d'affichage (optionnel) |

### Supprimer une image

```
DELETE /api/images/{id}
```

Supprime une image.

### Réordonner les images

```
POST /api/images/reorder
```

Réordonne les images d'un modèle.

#### Paramètres de la requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| model_type | string | Type de modèle |
| model_id | int | ID du modèle |
| image_ids | int[] | IDs des images dans l'ordre souhaité |

## Utilisation dans les modèles

Les modèles suivants ont été mis à jour pour supporter les images :

- Produit
- Categorie
- SousCategorie
- sous_sousCategorie
- Collection
- Marque
- ProduitVariante

Chaque modèle dispose des méthodes suivantes :

```php
// Récupérer toutes les images
$model->images()

// Récupérer l'image principale
$model->primaryImage

// Récupérer l'URL de l'image principale
$model->primaryImageUrl
```

## Tailles des miniatures

Le système génère automatiquement des miniatures pour chaque image téléchargée :

| Taille | Dimensions |
|--------|------------|
| small | 150x150 |
| medium | 300x300 |
| large | 600x600 |

Pour accéder à une miniature :

```php
$image->getThumbnailUrl('small');
```

## Optimisation des images

Les images sont automatiquement optimisées lors du téléchargement :

- Redimensionnement si nécessaire
- Compression avec un niveau de qualité configurable
- Conservation du ratio d'aspect

## Migration depuis les anciens champs d'image

Les modèles conservent temporairement les anciens champs d'image pour assurer la compatibilité. Ces champs sont maintenant tous nullable pour permettre l'utilisation du nouveau système d'images :

- Produit: `image_produit` (nullable)
- Categorie: `image_categorie` (nullable)
- Collection: `image` (nullable)
- Marque: `logo_marque` (nullable)

Ces champs sont utilisés comme fallback si aucune image n'est associée via la nouvelle relation. Lors de la création d'un nouveau produit, catégorie, etc., il n'est plus nécessaire de fournir une valeur pour ces champs.

## Exemple d'utilisation frontend

### Téléchargement d'une image

```javascript
const formData = new FormData();
formData.append('model_type', 'produit');
formData.append('model_id', 1);
formData.append('image', fileInput.files[0]);
formData.append('is_primary', true);
formData.append('alt_text', 'Description du produit');

const response = await fetch('/api/images/upload', {
  method: 'POST',
  body: formData,
  credentials: 'include'
});

const result = await response.json();
console.log(result.url); // URL de l'image téléchargée
```

### Affichage d'une image avec miniatures

```html
<!-- Image principale -->
<img src="{{ produit.primaryImageUrl }}" alt="{{ produit.nom_produit }}" />

<!-- Galerie d'images avec miniatures -->
<div class="gallery">
  @foreach(produit.images as image)
    <img
      src="{{ image.getThumbnailUrl('small') }}"
      data-full="{{ image.url }}"
      alt="{{ image.alt_text }}"
      title="{{ image.title }}"
    />
  @endforeach
</div>
```
