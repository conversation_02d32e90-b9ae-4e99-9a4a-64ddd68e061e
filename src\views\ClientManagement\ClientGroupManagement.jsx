import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Form, Table, Al<PERSON>, Spinner, <PERSON><PERSON>, Badge, InputGroup } from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaUsers, FaSearch, FaFilter, FaSort, FaEye } from 'react-icons/fa';
import { fetchClientGroups, createClientGroup, updateClientGroup, deleteClientGroup } from '../../services/clientService';

const ClientGroupManagement = () => {
  // State for client groups
  const [clientGroups, setClientGroups] = useState([]);
  const [groupForm, setGroupForm] = useState({
    name: '',
    description: '',
    discount_percentage: 0,
    is_active: true
  });
  const [editingGroupId, setEditingGroupId] = useState(null);
  const [groupLoading, setGroupLoading] = useState(false);
  const [groupSubmitting, setGroupSubmitting] = useState(false);

  // UI state
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredGroups, setFilteredGroups] = useState([]);

  // Load client groups
  const loadClientGroups = async () => {
    setGroupLoading(true);
    setError('');
    try {
      const data = await fetchClientGroups();
      setClientGroups(data);
      setFilteredGroups(data);
    } catch (e) {
      setError(`Error loading client groups: ${e.message}`);
    }
    setGroupLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadClientGroups();
  }, []);

  // Filter groups when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredGroups(clientGroups);
    } else {
      const filtered = clientGroups.filter(
        (group) =>
          group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredGroups(filtered);
    }
  }, [searchTerm, clientGroups]);

  // Handle group form changes
  const handleGroupChange = (e) => {
    const { name, value, type, checked } = e.target;
    setGroupForm({
      ...groupForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Submit group form
  const handleGroupSubmit = async () => {
    if (!groupForm.name) {
      setError('Group name is required');
      return;
    }

    setGroupSubmitting(true);
    setError('');

    try {
      if (editingGroupId) {
        await updateClientGroup(editingGroupId, groupForm);
        setSuccess('Client group updated successfully');
      } else {
        await createClientGroup(groupForm);
        setSuccess('Client group created successfully');
      }

      setShowGroupModal(false);
      resetGroupForm();
      loadClientGroups();
    } catch (e) {
      setError(`Error saving client group: ${e.message}`);
    }

    setGroupSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Reset group form
  const resetGroupForm = () => {
    setGroupForm({
      name: '',
      description: '',
      discount_percentage: 0,
      is_active: true
    });
    setEditingGroupId(null);
  };

  // Edit group
  const handleEditGroup = (group) => {
    setGroupForm({
      name: group.name,
      description: group.description || '',
      discount_percentage: group.discount_percentage || 0,
      is_active: group.is_active
    });
    setEditingGroupId(group.id);
    setShowGroupModal(true);
  };

  // Confirm delete
  const confirmDeleteGroup = (group) => {
    setGroupToDelete(group);
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDeleteGroup = async () => {
    setError('');

    try {
      await deleteClientGroup(groupToDelete.id);
      loadClientGroups();
      setSuccess('Client group deleted successfully');
    } catch (e) {
      setError(`Error deleting client group: ${e.message}`);
    }

    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaUsers className="me-2" />
          Client Group Management
        </h2>
        <p className="text-muted">Manage client groups and their discount settings.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Search and Actions */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control placeholder="Search client groups..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
              </InputGroup>
            </Col>
            <Col md={6} className="text-md-end mt-3 mt-md-0">
              <Button
                variant="primary"
                onClick={() => {
                  resetGroupForm();
                  setShowGroupModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Add New Group
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Client Groups List */}
      <Card className="shadow-sm border-0">
        <Card.Body className="p-0">
          {groupLoading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Loading client groups...</p>
            </div>
          ) : filteredGroups.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <FaUsers style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">No client groups found.</p>
              <Button
                variant="primary"
                onClick={() => {
                  resetGroupForm();
                  setShowGroupModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Create First Group
              </Button>
            </div>
          ) : (
            <Table hover responsive className="align-middle mb-0">
              <thead className="bg-light">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Description</th>
                  <th>Discount</th>
                  <th>Status</th>
                  <th>Clients</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredGroups.map((group) => (
                  <tr key={group.id}>
                    <td>{group.id}</td>
                    <td>
                      <span className="fw-medium">{group.name}</span>
                    </td>
                    <td>
                      <div className="text-truncate" style={{ maxWidth: '200px' }}>
                        {group.description || <span className="text-muted fst-italic">No description</span>}
                      </div>
                    </td>
                    <td>
                      {group.discount_percentage > 0 ? (
                        <Badge bg="success">{group.discount_percentage}%</Badge>
                      ) : (
                        <span className="text-muted">No discount</span>
                      )}
                    </td>
                    <td>{group.is_active ? <Badge bg="success">Active</Badge> : <Badge bg="secondary">Inactive</Badge>}</td>
                    <td>
                      <Badge bg="info">{group.clients_count || 0} clients</Badge>
                    </td>
                    <td>
                      <Button size="sm" variant="outline-primary" className="me-1" onClick={() => handleEditGroup(group)}>
                        <FaPencilAlt className="me-1" /> Edit
                      </Button>
                      <Button size="sm" variant="outline-danger" onClick={() => confirmDeleteGroup(group)}>
                        <FaTrashAlt className="me-1" /> Delete
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Group Modal */}
      <Modal show={showGroupModal} onHide={() => setShowGroupModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{editingGroupId ? 'Edit Client Group' : 'Add New Client Group'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={groupForm.name}
                onChange={handleGroupChange}
                placeholder="Enter group name"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={groupForm.description}
                onChange={handleGroupChange}
                placeholder="Enter group description"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Discount Percentage</Form.Label>
              <Form.Control
                type="number"
                name="discount_percentage"
                value={groupForm.discount_percentage}
                onChange={handleGroupChange}
                min="0"
                max="100"
              />
              <Form.Text className="text-muted">Default discount percentage for clients in this group (0-100)</Form.Text>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                id="is_active"
                name="is_active"
                label="Active"
                checked={groupForm.is_active}
                onChange={handleGroupChange}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowGroupModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleGroupSubmit} disabled={groupSubmitting}>
            {groupSubmitting ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Processing...
              </>
            ) : (
              'Save Group'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete the client group "{groupToDelete?.name}"?
          {groupToDelete?.clients_count > 0 && (
            <Alert variant="warning" className="mt-3">
              <strong>Warning:</strong> This group has {groupToDelete.clients_count} clients assigned to it. Deleting this group will remove
              these clients from the group.
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteGroup}>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ClientGroupManagement;
