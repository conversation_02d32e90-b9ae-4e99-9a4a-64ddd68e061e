# Flux de Gestion des Remises

Ce document décrit les différents types de remises disponibles dans le système et comment ils sont appliqués lors de la création de commandes.

## Types de remises

Le système prend en charge quatre types de remises, appliqués selon une hiérarchie précise:

1. **Remise personnelle**: Remise spécifique à un client individuel
2. **Remise partenaire**: Remise accordée aux clients de type "partenaire"
3. **Remise point de vente**: Remise accordée aux clients associés à un point de vente
4. **Remise groupe**: Remise accordée aux clients appartenant à un groupe
5. **Remise commande**: Remise spécifique à une commande particulière

## Hiérarchie des remises

Lors du calcul de la remise effective pour une commande, le système applique la remise la plus avantageuse selon la hiérarchie suivante:

1. **Remise commande**: Si une remise spécifique est définie pour la commande, elle est utilisée en priorité
2. **Remise personnelle**: Si le client a une remise personnelle, elle est utilisée
3. **Remise selon le type de client**:
   - Pour les partenaires: remise définie au niveau du partenaire
   - Pour les points de vente: remise définie au niveau du point de vente
   - Pour les groupes: remise définie au niveau du groupe
   - Pour les clients normaux: pas de remise par défaut

## Flux de configuration des remises

### 1. Configuration d'une remise personnelle

Un administrateur peut définir une remise personnelle pour un client spécifique.

```
PUT /api/clients/{id}/remise
```

**Corps de la requête:**
```json
{
  "remise_personnelle": 12.75
}
```

**Réponse:**
```json
{
  "id": 1,
  "name": "Youssef Mrabet",
  "email": "<EMAIL>",
  "remise_personnelle": 12.75,
  "type_client": "normal",
  "remise_effective": 12.75
}
```

### 2. Configuration d'une remise partenaire

Un administrateur peut créer un partenaire et définir sa remise.

```
POST /api/partenaires
```

**Corps de la requête:**
```json
{
  "user_id": 2,
  "remise": 15.00,
  "description": "Partenaire premium",
  "statut": "actif"
}
```

**Réponse:**
```json
{
  "id": 1,
  "user_id": 2,
  "remise": 15.00,
  "description": "Partenaire premium",
  "statut": "actif",
  "created_at": "2025-04-04T17:00:00.000000Z",
  "updated_at": "2025-04-04T17:00:00.000000Z",
  "user": {
    "id": 2,
    "name": "Entreprise ABC",
    "email": "<EMAIL>",
    "roles": ["client", "partenaire"],
    "type_client": "partenaire"
  }
}
```

### 3. Configuration d'une remise point de vente

Un administrateur peut créer un point de vente avec une remise et y associer des clients.

```
POST /api/points-de-vente
```

**Corps de la requête:**
```json
{
  "nom": "Boutique Centre Ville",
  "adresse": "123 Rue Principale",
  "telephone": "71234567",
  "email": "<EMAIL>",
  "remise": 8.50,
  "description": "Point de vente principal",
  "statut": "actif"
}
```

Puis associer un client:

```
POST /api/points-de-vente/1/clients
```

**Corps de la requête:**
```json
{
  "user_id": 3
}
```

### 4. Configuration d'une remise groupe

Un administrateur peut créer un groupe de clients avec une remise et y associer des clients.

```
POST /api/groupes-clients
```

**Corps de la requête:**
```json
{
  "nom": "Clients Premium",
  "description": "Groupe de clients premium",
  "remise": 10.00,
  "statut": "actif"
}
```

Puis associer un client:

```
POST /api/groupes-clients/1/clients
```

**Corps de la requête:**
```json
{
  "user_id": 4
}
```

### 5. Configuration d'une remise commande

Lors de la création d'une commande, une remise spécifique peut être définie:

```
POST /api/commandes
```

**Corps de la requête:**
```json
{
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "remise_commande": 20.00,
  "produits": [
    {
      "id": 1,
      "quantite": 1,
      "prix_unitaire": 1299.99
    }
  ]
}
```

## Exemple de calcul de remise

### Scénario 1: Client avec remise personnelle

- Client: Youssef Mrabet (ID: 1)
- Remise personnelle: 12.75%
- Type de client: normal
- Commande: 1 Laptop Pro X (1299.99 €)
- Remise commande: non spécifiée

**Calcul:**
1. Sous-total: 1 × 1299.99 = 1299.99 €
2. Remise effective: 12.75% (remise personnelle)
3. Montant de la remise: 1299.99 × 12.75% = 165.75 €
4. Total final: 1299.99 - 165.75 = 1134.24 €

### Scénario 2: Client partenaire

- Client: Entreprise ABC (ID: 2)
- Remise personnelle: non définie
- Type de client: partenaire
- Remise partenaire: 15.00%
- Commande: 1 Laptop Pro X (1299.99 €)
- Remise commande: non spécifiée

**Calcul:**
1. Sous-total: 1 × 1299.99 = 1299.99 €
2. Remise effective: 15.00% (remise partenaire)
3. Montant de la remise: 1299.99 × 15.00% = 195.00 €
4. Total final: 1299.99 - 195.00 = 1104.99 €

### Scénario 3: Client avec remise commande spécifique

- Client: Youssef Mrabet (ID: 1)
- Remise personnelle: 12.75%
- Type de client: normal
- Commande: 1 Laptop Pro X (1299.99 €)
- Remise commande: 20.00%

**Calcul:**
1. Sous-total: 1 × 1299.99 = 1299.99 €
2. Remise effective: 20.00% (remise commande, plus avantageuse que la remise personnelle)
3. Montant de la remise: 1299.99 × 20.00% = 260.00 €
4. Total final: 1299.99 - 260.00 = 1039.99 €

## Diagramme de flux

```
┌─────────────────┐
│ Création d'une  │
│    commande     │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Calcul du       │
│  sous-total     │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     Oui     ┌─────────────────┐
│ Remise commande │────────────>│ Utiliser remise │
│   spécifiée?    │             │    commande     │
└────────┬────────┘             └─────────────────┘
         │ Non
         ▼
┌─────────────────┐     Oui     ┌─────────────────┐
│ Client a remise │────────────>│ Utiliser remise │
│  personnelle?   │             │   personnelle   │
└────────┬────────┘             └─────────────────┘
         │ Non
         ▼
┌─────────────────┐
│ Vérifier type   │
│    client       │
└────────┬────────┘
         │
         ├─────────────┐
         │             │
         ▼             ▼             ▼
┌─────────────────┐ ┌─────────────┐ ┌─────────────┐
│   Partenaire    │ │ Point de    │ │   Groupe    │
│                 │ │   vente     │ │             │
└────────┬────────┘ └──────┬──────┘ └──────┬──────┘
         │                 │               │
         ▼                 ▼               ▼
┌─────────────────┐ ┌─────────────┐ ┌─────────────┐
│ Utiliser remise │ │Utiliser     │ │Utiliser     │
│   partenaire    │ │remise PDV   │ │remise groupe│
└────────┬────────┘ └──────┬──────┘ └──────┬──────┘
         │                 │               │
         └─────────────────┴───────────────┘
                           │
                           ▼
                   ┌─────────────────┐
                   │ Appliquer remise│
                   │ au sous-total   │
                   └─────────────────┘
```
