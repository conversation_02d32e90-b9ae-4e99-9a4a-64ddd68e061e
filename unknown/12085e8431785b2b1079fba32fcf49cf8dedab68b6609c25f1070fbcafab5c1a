# Synchronisation des Utilisateurs

## Introduction

Ce document explique comment le système gère la synchronisation des utilisateurs entre Keycloak et la base de donn<PERSON>, ainsi que la gestion des rôles et des profils de remise.

## Architecture d'authentification

Le système utilise trois clients Keycloak :

1. **api-client** : Utilisé par le backend pour l'authentification et l'autorisation
2. **backoffice-client** : Utilisé par l'interface d'administration
3. **frontoffice-client** : Utilisé par l'interface client

Chaque client est configuré pour inclure les rôles du realm et les rôles spécifiques au client dans les tokens JWT.

## Processus de synchronisation

Lorsqu'un utilisateur s'authentifie via Keycloak, le système effectue les opérations suivantes :

1. **Vérification de l'existence de l'utilisateur** :
   - Recherche par `keycloak_id`
   - Si non trouvé, recherche par `email`
   - Si toujours non trouvé, création d'un nouvel utilisateur

2. **Extraction des rôles du token** :
   - Les rôles du realm sont extraits du token JWT
   - Les rôles spécifiques au client sont également extraits
   - Tous les rôles sont combinés dans un tableau unique

3. **Attribution des rôles** :
   - Les rôles définis dans Keycloak sont synchronisés
   - Le rôle 'client' est automatiquement ajouté à tous les utilisateurs

4. **Détermination du profil de remise** :
   - Le profil de remise est déterminé en fonction des rôles de l'utilisateur
   - Si l'utilisateur a le rôle 'partenaire', son profil est défini comme 'premium'
   - Sinon, le profil est défini par défaut à 'standard'

## Implémentation technique

### Extraction des rôles du token

```php
protected function extractRolesFromToken(object $decoded): array
{
    // Extract realm roles
    $realmRoles = $decoded->realm_access->roles ?? [];

    // Extract client-specific roles if available
    $clientRoles = [];
    $clientId = config('services.keycloak.client_id');
    if (isset($decoded->resource_access->$clientId)) {
        $clientRoles = $decoded->resource_access->$clientId->roles ?? [];
    }

    // Combine all roles
    return array_unique(array_merge($realmRoles, $clientRoles));
}
```

### Méthode de synchronisation

La méthode `syncWithKeycloak` dans le modèle `User` gère la synchronisation :

```php
public static function syncWithKeycloak(array $keycloakUser, array $roles): User
{
    // First try to find user by keycloak_id
    $user = static::where('keycloak_id', $keycloakUser['sub'])->first();

    // If not found by keycloak_id, try to find by email
    if (!$user) {
        $user = static::where('email', $keycloakUser['email'])->first();
    }

    // Ensure the client role is included
    if (!in_array('client', $roles)) {
        $roles[] = 'client';
    }

    // Determine remise profile based on roles
    $profilRemise = 'standard';

    if ($user) {
        // For existing users, keep their profile if already set
        $profilRemise = $user->getRawOriginal('profil_remise') ?: $user->getProfilRemiseAttribute(null);

        // Update existing user
        $user->update([
            'keycloak_id' => $keycloakUser['sub'],
            'name' => $keycloakUser['name'],
            'email' => $keycloakUser['email'],
            'roles' => $roles
        ]);
    } else {
        // Create new user
        $user = static::create([
            'keycloak_id' => $keycloakUser['sub'],
            'name' => $keycloakUser['name'],
            'email' => $keycloakUser['email'],
            'password' => bcrypt(Str::random(32)),
            'email_verified_at' => now(),
            'roles' => $roles,
            'profil_remise' => $profilRemise
        ]);
    }

    // Ensure type_client and roles are consistent
    self::ensureRoleTypeConsistency($user);

    return $user;
}
```

### Méthode de cohérence

La méthode `ensureRoleTypeConsistency` garantit la cohérence entre les rôles et le profil de remise :

```php
protected static function ensureRoleTypeConsistency(User $user): void
{
    $roles = $user->roles ?? [];
    $updated = false;

    // Ensure all users have the client role
    if (!in_array('client', $roles)) {
        $roles[] = 'client';
        $updated = true;
    }

    // Determine profil_remise based on roles
    $newProfilRemise = null;

    // Check for partenaire role
    if (in_array('partenaire', $roles)) {
        $newProfilRemise = 'premium';
    }
    // Check for point_de_vente role
    else if (in_array('point_de_vente', $roles)) {
        $newProfilRemise = 'affilie';
    }
    // Check for groupe role (if you have one)
    else if (in_array('groupe', $roles)) {
        $newProfilRemise = 'groupe';
    }
    // Default to standard
    else {
        $newProfilRemise = 'standard';
    }

    // Update profil_remise if it has changed
    if ($newProfilRemise && $user->profil_remise !== $newProfilRemise) {
        $user->profil_remise = $newProfilRemise;
        $updated = true;
    }

    // Update roles if changed
    if ($updated) {
        $user->save();
    }
}
```

## Synchronisation bidirectionnelle

Le système prend également en charge la synchronisation bidirectionnelle entre les rôles Keycloak et les profils de remise :

1. **De Keycloak vers l'application** :
   - Lorsqu'un utilisateur s'authentifie, ses rôles sont extraits du token
   - Le profil de remise est déterminé en fonction des rôles

2. **De l'application vers Keycloak** :
   - Lorsqu'un profil de remise est modifié dans l'application, les rôles correspondants sont mis à jour dans Keycloak
   - Par exemple, si le profil est changé en 'premium', le rôle 'partenaire' est ajouté dans Keycloak

### Service de gestion des rôles Keycloak

Le service `KeycloakRoleService` permet d'interagir avec l'API Admin de Keycloak pour gérer les rôles des utilisateurs :

```php
// Ajouter un rôle à un utilisateur dans Keycloak
public function addRoleToUser(string $keycloakId, string $roleName): bool
{
    // Code pour ajouter un rôle à un utilisateur dans Keycloak
}

// Supprimer un rôle d'un utilisateur dans Keycloak
public function removeRoleFromUser(string $keycloakId, string $roleName): bool
{
    // Code pour supprimer un rôle d'un utilisateur dans Keycloak
}
```

## Commande de mise à jour

Une commande Artisan est disponible pour mettre à jour les utilisateurs existants et assurer la cohérence entre les rôles et les profils de remise :

```bash
php artisan users:migrate-profiles
```

Cette commande :

1. Migre les données des anciens types de clients vers les nouveaux profils de remise
2. S'assure que les rôles et les profils de remise sont cohérents

## Filtrage dans l'API des clients

L'API des clients (`ClientController`) filtre les utilisateurs qui ont le rôle 'client' :

```php
// Base query: get all users with the 'client' role
$query = User::whereJsonContains('roles', 'client');
```

Cela signifie que seuls les utilisateurs ayant le rôle 'client' apparaîtront dans l'API des clients, indépendamment des autres rôles qu'ils pourraient avoir.

## Bonnes pratiques

1. **Gestion des rôles dans Keycloak** : Utilisez Keycloak pour gérer les rôles des utilisateurs plutôt que de les modifier directement dans la base de données.

2. **Cohérence des données** : Exécutez régulièrement la commande `users:migrate-profiles` pour maintenir la cohérence des données.

3. **Filtrage approprié** : Lors de la recherche d'utilisateurs, utilisez le filtrage par rôle pour le contrôle d'accès et le filtrage par profil de remise pour la logique métier.

4. **Configuration de Keycloak** : Assurez-vous que les clients Keycloak sont correctement configurés pour inclure les rôles dans les tokens JWT.

5. **Sécurité de l'API Admin** : Protégez l'accès à l'API Admin de Keycloak en utilisant un client dédié avec des permissions limitées.
