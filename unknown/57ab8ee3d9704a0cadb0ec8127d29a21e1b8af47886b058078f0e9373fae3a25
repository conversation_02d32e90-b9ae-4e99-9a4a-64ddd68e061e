# Jihene-Line API Documentation

## Authentication

### Verify Token
`POST /auth/verify`

Verifies a Keycloak token and establishes a session.

**Request Body:**
```json
{
    "access_token": "string",
    "refresh_token": "string",
    "id_token": "string"
}
```

**Response:**
```json
{
    "message": "Verification successful",
    "user": {
        "name": "string",
        "email": "string"
    },
    "roles": ["string"]
}
```

### Logout
`POST /auth/logout`

Ends the current session.

**Headers:**
- Cookie: access_token=<token>

**Response:**
```json
{
    "message": "Logged out successfully"
}
```