# Gestion des Collections

## Introduction

Le système permet de gérer des collections de produits. Une collection est un regroupement thématique ou promotionnel de produits qui peut être utilisé pour mettre en avant certains produits sur le site e-commerce. Les collections peuvent être temporaires (avec des dates de début et de fin) ou permanentes.

## Endpoints API

### Récupérer toutes les collections

```http
GET /api/collections
```

Retourne la liste de toutes les collections.

#### Paramètres de filtrage (optionnels)

| Paramètre      | Type    | Description                                |
|----------------|---------|--------------------------------------------|
| active         | boolean | Filtrer les collections actives uniquement |
| with_produits  | boolean | Inclure les produits dans la réponse       |

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "nom": "Collection Été 2025",
    "description": "Produits tendance pour l'été",
    "image": "ete_2025.jpg",
    "active": true,
    "date_debut": "2025-05-01",
    "date_fin": "2025-08-31",
    "created_at": "2025-04-04T14:30:00.000000Z",
    "updated_at": "2025-04-04T14:30:00.000000Z"
  },
  {
    "id": 2,
    "nom": "Nouveautés Tech",
    "description": "Les dernières innovations technologiques",
    "image": "nouveautes_tech.jpg",
    "active": true,
    "date_debut": null,
    "date_fin": null,
    "created_at": "2025-04-04T15:00:00.000000Z",
    "updated_at": "2025-04-04T15:00:00.000000Z"
  }
]
```

### Récupérer une collection spécifique

```http
GET /api/collections/{id}
```

Retourne les détails d'une collection spécifique avec ses produits.

#### Exemple de réponse

```json
{
  "id": 1,
  "nom": "Collection Été 2025",
  "description": "Produits tendance pour l'été",
  "image": "ete_2025.jpg",
  "active": true,
  "date_debut": "2025-05-01",
  "date_fin": "2025-08-31",
  "created_at": "2025-04-04T14:30:00.000000Z",
  "updated_at": "2025-04-04T14:30:00.000000Z",
  "produits": [
    {
      "id": 1,
      "nom_produit": "Smartphone XYZ",
      "prix_produit": 499.99,
      "quantite_produit": 50,
      "description_produit": "Smartphone dernière génération",
      "image_produit": "smartphone_xyz.jpg",
      "marque_id": 1,
      "sous_sous_categorie_id": 5,
      "pivot": {
        "collection_id": 1,
        "produit_id": 1,
        "ordre": 1,
        "featured": true,
        "created_at": "2025-04-04T14:30:00.000000Z",
        "updated_at": "2025-04-04T14:30:00.000000Z"
      }
    },
    {
      "id": 2,
      "nom_produit": "Tablette ABC",
      "prix_produit": 349.99,
      "quantite_produit": 30,
      "description_produit": "Tablette tactile haute résolution",
      "image_produit": "tablette_abc.jpg",
      "marque_id": 1,
      "sous_sous_categorie_id": 6,
      "pivot": {
        "collection_id": 1,
        "produit_id": 2,
        "ordre": 2,
        "featured": false,
        "created_at": "2025-04-04T14:30:00.000000Z",
        "updated_at": "2025-04-04T14:30:00.000000Z"
      }
    }
  ]
}
```

### Créer une nouvelle collection

```http
POST /api/collections
```

Crée une nouvelle collection avec des produits optionnels.

#### Paramètres de la requête

| Paramètre      | Type    | Description                                |
|----------------|---------|--------------------------------------------|
| nom            | string  | Nom de la collection (obligatoire)         |
| description    | string  | Description de la collection               |
| image          | string  | Nom du fichier image                       |
| active         | boolean | Statut actif de la collection              |
| date_debut     | date    | Date de début (format: YYYY-MM-DD)         |
| date_fin       | date    | Date de fin (format: YYYY-MM-DD)           |
| produits       | array   | Tableau des produits à inclure             |
| produits.*.id  | integer | ID du produit                              |
| produits.*.ordre | integer | Ordre d'affichage du produit             |
| produits.*.featured | boolean | Produit mis en avant                  |

#### Exemple de requête

```json
{
  "nom": "Collection Été 2025",
  "description": "Produits tendance pour l'été",
  "image": "ete_2025.jpg",
  "active": true,
  "date_debut": "2025-05-01",
  "date_fin": "2025-08-31",
  "produits": [
    {
      "id": 1,
      "ordre": 1,
      "featured": true
    },
    {
      "id": 2,
      "ordre": 2,
      "featured": false
    }
  ]
}
```

### Mettre à jour une collection

```http
PUT /api/collections/{id}
```

Met à jour les informations d'une collection existante.

#### Paramètres de la requête

Mêmes paramètres que pour la création, tous optionnels sauf `nom` s'il est fourni.

### Supprimer une collection

```http
DELETE /api/collections/{id}
```

Supprime une collection existante.

### Ajouter des produits à une collection

```http
POST /api/collections/{id}/produits
```

Ajoute un ou plusieurs produits à une collection existante.

#### Paramètres de la requête

| Paramètre      | Type    | Description                                |
|----------------|---------|--------------------------------------------|
| produits       | array   | Tableau des produits à inclure (obligatoire) |
| produits.*.id  | integer | ID du produit (obligatoire)                |
| produits.*.ordre | integer | Ordre d'affichage du produit             |
| produits.*.featured | boolean | Produit mis en avant                  |

#### Exemple de requête

```json
{
  "produits": [
    {
      "id": 3,
      "ordre": 3,
      "featured": true
    },
    {
      "id": 4,
      "ordre": 4,
      "featured": false
    }
  ]
}
```

### Supprimer un produit d'une collection

```http
DELETE /api/collections/{id}/produits/{produitId}
```

Supprime un produit d'une collection.

### Récupérer les produits d'une collection

```http
GET /api/collections/{id}/produits
```

Retourne tous les produits d'une collection spécifique.

### Récupérer les produits mis en avant d'une collection

```http
GET /api/collections/{id}/produits-featured
```

Retourne les produits mis en avant (featured) d'une collection spécifique.

## Gestion des collections temporaires

Les collections peuvent être configurées pour être actives uniquement pendant une période spécifique en définissant les champs `date_debut` et `date_fin`. Lorsque vous utilisez le paramètre `active=true` dans la requête GET, seules les collections actuellement actives (selon la date du jour) seront retournées.

## Ordre d'affichage des produits

Chaque produit dans une collection peut avoir un ordre d'affichage spécifié par le champ `ordre`. Les produits sont retournés triés par cet ordre, ce qui permet de contrôler leur position dans l'interface utilisateur.

## Mise en avant de produits

Le champ `featured` permet de marquer certains produits comme "mis en avant" dans une collection. Ces produits peuvent être récupérés séparément via l'endpoint `/api/collections/{id}/produits-featured` et peuvent être affichés de manière plus proéminente dans l'interface utilisateur.

## Promotions sur les collections

### Associer une promotion à une collection

```http
POST /api/collections/{id}/promotions
```

Associe une promotion existante à une collection.

#### Paramètres de la requête

| Paramètre      | Type    | Description                                |
|----------------|---------|-----------------------------------------|
| promotion_id   | integer | ID de la promotion (obligatoire)         |
| date_debut     | date    | Date de début spécifique (optionnelle)   |
| date_fin       | date    | Date de fin spécifique (optionnelle)     |

#### Exemple de requête

```json
{
  "promotion_id": 1,
  "date_debut": "2025-06-01",
  "date_fin": "2025-06-15"
}
```

### Détacher une promotion d'une collection

```http
DELETE /api/collections/{id}/promotions/{promotionId}
```

Détache une promotion d'une collection.

### Filtrer les produits par collection et promotion

Vous pouvez combiner les paramètres de filtrage pour obtenir les produits d'une collection spécifique qui sont également en promotion :

```http
GET /api/produits?collection_id=1&en_promotion=true
```

Cette requête retournera tous les produits qui appartiennent à la collection avec l'ID 1 et qui ont au moins une promotion active.
