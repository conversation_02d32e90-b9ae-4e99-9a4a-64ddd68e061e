import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Button, Row, Col, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Table } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import styled from 'styled-components';

// Styled components for responsive design
const ResponsiveContainer = styled(Container)`
  padding: 2rem 1rem;

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
`;

const PromotionCard = styled(Card)`
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border: none;

  @media (max-width: 768px) {
    margin-bottom: 1rem;
  }
`;

const StepContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: center;
  }
`;

const Step = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  padding: 0 1rem;

  @media (max-width: 576px) {
    margin-bottom: 1rem;
  }
`;

const StepCircle = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  background-color: ${(props) => (props.active ? '#3a8dde' : '#e9ecef')};
  color: ${(props) => (props.active ? 'white' : '#6c757d')};
  font-weight: ${(props) => (props.active ? 'bold' : 'normal')};
`;

const StepConnector = styled.div`
  flex-grow: 1;
  height: 2px;
  background-color: ${(props) => (props.active ? '#3a8dde' : '#e9ecef')};
  margin: 0 -1rem;
  position: relative;
  top: 20px;
  z-index: 0;

  @media (max-width: 576px) {
    display: none;
  }
`;

const ResponsiveTable = styled(Table)`
  @media (max-width: 768px) {
    font-size: 0.9rem;

    th,
    td {
      padding: 0.5rem;
    }
  }
`;

const FormSection = styled.div`
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    margin-bottom: 1.5rem;
  }
`;

const DatePickerWrapper = styled.div`
  .react-datepicker-wrapper {
    width: 100%;
  }
`;

const GestionPromotions = () => {
  const API_URL = 'https://laravel-api.fly.dev/api';

  const colors = {
    primaryDark: '#2a3f5f',
    primaryLight: '#3a537b',
    accent: '#3a8dde',
    success: '#2ecc71',
    danger: '#e74c3c',
    warning: '#f39c12'
  };

  // États
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    nom: '',
    code: '',
    description: '',
    type: 'pourcentage',
    valeur: '',
    statut: 'active',
    date_debut: null,
    date_fin: null,
    priorite: 10,
    cumulable: false,
    produits: [],
    collections: [],
    profils_remise: []
  });
  const [allProduits, setAllProduits] = useState([]);
  const [allCollections, setAllCollections] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showAddProduit, setShowAddProduit] = useState(false);
  const [showAddCollection, setShowAddCollection] = useState(false);
  const [selectedProduits, setSelectedProduits] = useState([]);
  const [selectedCollections, setSelectedCollections] = useState([]);
  const [showPromotionsList, setShowPromotionsList] = useState(false);
  const [promotions, setPromotions] = useState([]);
  const [promotionsLoading, setPromotionsLoading] = useState(false);

  // Types et options
  const typesPromotion = [
    { value: 'pourcentage', label: 'Pourcentage' },
    { value: 'montant_fixe', label: 'Montant fixe' },
    { value: 'gratuit', label: 'Gratuit' }
  ];

  const statutsPromotion = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'programmee', label: 'Programmée' }
  ];

  const profilsRemise = [
    { value: 'standard', label: 'Standard' },
    { value: 'premium', label: 'Premium' },
    { value: 'affilie', label: 'Affilié' },
    { value: 'groupe', label: 'Groupe' }
  ];

  // Nombre total d'étapes
  const totalSteps = 3;

  // Chargement initial des données
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [produitsRes, collectionsRes] = await Promise.all([fetch(`${API_URL}/produits`), fetch(`${API_URL}/collections`)]);

        if (!produitsRes.ok || !collectionsRes.ok) {
          throw new Error('Erreur de chargement des données');
        }

        const [produitsData, collectionsData] = await Promise.all([produitsRes.json(), collectionsRes.json()]);

        setAllProduits(produitsData.data || produitsData);
        setAllCollections(collectionsData.data || collectionsData);
        setLoading(false);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les données initiales');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Charger les promotions
  const fetchPromotions = async () => {
    try {
      setPromotionsLoading(true);
      const response = await fetch(`${API_URL}/promotions`);
      if (!response.ok) throw new Error('Erreur de chargement');
      const data = await response.json();
      setPromotions(data.data || data);
      setPromotionsLoading(false);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de charger la liste des promotions');
      setPromotionsLoading(false);
    }
  };

  // Gestion des changements
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Gestion des dates
  const handleDateChange = (date, field) => {
    setFormData({
      ...formData,
      [field]: date
    });
  };

  // Gestion des sélections multiples
  const handleMultiSelect = (e) => {
    const { name, options } = e.target;
    const selected = [];
    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        selected.push(options[i].value);
      }
    }
    setFormData({
      ...formData,
      [name]: selected
    });
  };

  // Ajout de produits
  const addProduits = () => {
    setFormData({
      ...formData,
      produits: [...formData.produits, ...selectedProduits]
    });
    setSelectedProduits([]);
    setShowAddProduit(false);
  };

  // Ajout de collections
  const addCollections = () => {
    setFormData({
      ...formData,
      collections: [...formData.collections, ...selectedCollections]
    });
    setSelectedCollections([]);
    setShowAddCollection(false);
  };

  // Suppression d'un produit
  const removeProduit = (id) => {
    setFormData({
      ...formData,
      produits: formData.produits.filter((prodId) => prodId !== id)
    });
  };

  // Suppression d'une collection
  const removeCollection = (id) => {
    setFormData({
      ...formData,
      collections: formData.collections.filter((colId) => colId !== id)
    });
  };

  // Navigation entre étapes
  const nextStep = () => {
    if (currentStep === 1 && (!formData.nom || !formData.type || !formData.valeur)) {
      setError('Veuillez remplir tous les champs obligatoires');
      return;
    }

    setError(null);
    setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  // Soumission du formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const payload = {
        ...formData,
        date_debut: formData.date_debut ? formData.date_debut.toISOString() : null,
        date_fin: formData.date_fin ? formData.date_fin.toISOString() : null
      };

      const response = await fetch(`${API_URL}/promotions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erreur lors de l'ajout");
      }

      const data = await response.json();

      setSuccess('Promotion créée avec succès!');
      setFormData({
        nom: '',
        code: '',
        description: '',
        type: 'pourcentage',
        valeur: '',
        statut: 'active',
        date_debut: null,
        date_fin: null,
        priorite: 10,
        cumulable: false,
        produits: [],
        collections: [],
        profils_remise: []
      });
      setCurrentStep(1);
    } catch (err) {
      console.error('Erreur:', err);
      setError(err.message || 'Erreur lors de la création de la promotion');
    } finally {
      setLoading(false);
    }
  };

  // Barre de progression personnalisée
  const CustomProgressBar = () => {
    return (
      <StepContainer>
        {[...Array(totalSteps)].map((_, i) => (
          <React.Fragment key={i}>
            <Step>
              <StepCircle active={currentStep > i + 1 || currentStep === i + 1}>
                {currentStep > i + 1 ? <i className="fas fa-check"></i> : i + 1}
              </StepCircle>
              <span className="small">
                {i === 0 && 'Informations'}
                {i === 1 && 'Cibles'}
                {i === 2 && 'Confirmation'}
              </span>
            </Step>
            {i < totalSteps - 1 && <StepConnector active={currentStep > i + 1} />}
          </React.Fragment>
        ))}
      </StepContainer>
    );
  };

  return (
    <ResponsiveContainer>
      {/* Titre principal */}
      <div className="text-center mb-5">
        <h1
          className="mb-3"
          style={{
            fontSize: '2.2rem',
            fontWeight: 600,
            color: colors.primaryDark,
            letterSpacing: '1px'
          }}
        >
          GESTION DES PROMOTIONS
        </h1>
        <div
          className="mx-auto"
          style={{
            height: '3px',
            width: '120px',
            background: 'linear-gradient(90deg, rgba(58,83,155,0.2) 0%, rgba(58,83,155,1) 50%, rgba(58,83,155,0.2) 100%)',
            borderRadius: '3px'
          }}
        />
      </div>

      {/* Messages d'alerte */}
      {error && (
        <Alert variant="danger" className="mb-4" onClose={() => setError(null)} dismissible>
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4" onClose={() => setSuccess(null)} dismissible>
          <i className="fas fa-check-circle me-2"></i>
          {success}
        </Alert>
      )}

      {/* Carte principale */}
      <PromotionCard>
        <Card.Body>
          <Form onSubmit={handleSubmit}>
            <CustomProgressBar />

            {/* Étape 1: Informations de base */}
            {currentStep === 1 && (
              <FormSection>
                <h5 className="mb-4 text-primary">Informations de la promotion</h5>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        Nom de la promotion <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="nom"
                        value={formData.nom}
                        onChange={handleChange}
                        placeholder="Nom de la promotion"
                        required
                      />
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Code promotionnel</Form.Label>
                      <Form.Control
                        type="text"
                        name="code"
                        value={formData.code}
                        onChange={handleChange}
                        placeholder="Code unique (optionnel)"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        rows={3}
                        placeholder="Description de la promotion"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        Type de promotion <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select name="type" value={formData.type} onChange={handleChange} required>
                        {typesPromotion.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        Valeur <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        type="number"
                        name="valeur"
                        value={formData.valeur}
                        onChange={handleChange}
                        placeholder={formData.type === 'pourcentage' ? '10' : '5'}
                        min="0"
                        step={formData.type === 'pourcentage' ? '0.1' : '0.01'}
                        required
                      />
                      <Form.Text className="text-muted">
                        {formData.type === 'pourcentage'
                          ? 'Pourcentage de réduction'
                          : formData.type === 'montant_fixe'
                            ? 'Montant fixe de réduction'
                            : 'Produit gratuit'}
                      </Form.Text>
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        Statut <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select name="statut" value={formData.statut} onChange={handleChange} required>
                        {statutsPromotion.map((statut) => (
                          <option key={statut.value} value={statut.value}>
                            {statut.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Date de début</Form.Label>
                      <DatePickerWrapper>
                        <DatePicker
                          selected={formData.date_debut}
                          onChange={(date) => handleDateChange(date, 'date_debut')}
                          selectsStart
                          startDate={formData.date_debut}
                          endDate={formData.date_fin}
                          placeholderText="Date de début (optionnel)"
                          className="form-control"
                          dateFormat="dd/MM/yyyy"
                          isClearable
                        />
                      </DatePickerWrapper>
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Date de fin</Form.Label>
                      <DatePickerWrapper>
                        <DatePicker
                          selected={formData.date_fin}
                          onChange={(date) => handleDateChange(date, 'date_fin')}
                          selectsEnd
                          startDate={formData.date_debut}
                          endDate={formData.date_fin}
                          minDate={formData.date_debut}
                          placeholderText="Date de fin (optionnel)"
                          className="form-control"
                          dateFormat="dd/MM/yyyy"
                          isClearable
                        />
                      </DatePickerWrapper>
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Priorité</Form.Label>
                      <Form.Control
                        type="number"
                        name="priorite"
                        value={formData.priorite}
                        onChange={handleChange}
                        min="1"
                        placeholder="10"
                      />
                      <Form.Text className="text-muted">Plus la valeur est élevée, plus la priorité est grande</Form.Text>
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Profils de remise autorisés</Form.Label>
                      <Form.Select name="profils_remise" multiple value={formData.profils_remise} onChange={handleMultiSelect} size="3">
                        {profilsRemise.map((profil) => (
                          <option key={profil.value} value={profil.value}>
                            {profil.label}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">Maintenez Ctrl pour sélectionner plusieurs options</Form.Text>
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        name="cumulable"
                        label="Promotion cumulable"
                        checked={formData.cumulable}
                        onChange={handleChange}
                      />
                      <Form.Text className="text-muted">Peut être combinée avec d'autres promotions</Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
              </FormSection>
            )}

            {/* Étape 2: Cibles de la promotion */}
            {currentStep === 2 && (
              <FormSection>
                <h5 className="mb-4 text-primary">Cibles de la promotion</h5>

                <Row>
                  <Col md={6}>
                    <Card className="mb-4">
                      <Card.Header className="d-flex justify-content-between align-items-center">
                        <span>Produits ciblés</span>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => setShowAddProduit(true)}
                          disabled={loading || allProduits.length === 0}
                        >
                          <i className="fas fa-plus me-1"></i>
                          Ajouter
                        </Button>
                      </Card.Header>
                      <Card.Body>
                        {formData.produits.length > 0 ? (
                          <div className="table-responsive">
                            <ResponsiveTable striped bordered hover size="sm">
                              <thead>
                                <tr>
                                  <th>Produit</th>
                                  <th>Actions</th>
                                </tr>
                              </thead>
                              <tbody>
                                {formData.produits.map((produitId) => {
                                  const produit = allProduits.find((p) => p.id == produitId);
                                  return (
                                    <tr key={produitId}>
                                      <td>{produit ? produit.nom_produit : `Produit #${produitId}`}</td>
                                      <td>
                                        <Button variant="outline-danger" size="sm" onClick={() => removeProduit(produitId)}>
                                          <i className="fas fa-trash"></i>
                                        </Button>
                                      </td>
                                    </tr>
                                  );
                                })}
                              </tbody>
                            </ResponsiveTable>
                          </div>
                        ) : (
                          <Alert variant="info" className="mb-0">
                            Aucun produit sélectionné
                          </Alert>
                        )}
                      </Card.Body>
                    </Card>
                  </Col>

                  <Col md={6}>
                    <Card className="mb-4">
                      <Card.Header className="d-flex justify-content-between align-items-center">
                        <span>Collections ciblées</span>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => setShowAddCollection(true)}
                          disabled={loading || allCollections.length === 0}
                        >
                          <i className="fas fa-plus me-1"></i>
                          Ajouter
                        </Button>
                      </Card.Header>
                      <Card.Body>
                        {formData.collections.length > 0 ? (
                          <div className="table-responsive">
                            <ResponsiveTable striped bordered hover size="sm">
                              <thead>
                                <tr>
                                  <th>Collection</th>
                                  <th>Actions</th>
                                </tr>
                              </thead>
                              <tbody>
                                {formData.collections.map((collectionId) => {
                                  const collection = allCollections.find((c) => c.id == collectionId);
                                  return (
                                    <tr key={collectionId}>
                                      <td>{collection ? collection.nom : `Collection #${collectionId}`}</td>
                                      <td>
                                        <Button variant="outline-danger" size="sm" onClick={() => removeCollection(collectionId)}>
                                          <i className="fas fa-trash"></i>
                                        </Button>
                                      </td>
                                    </tr>
                                  );
                                })}
                              </tbody>
                            </ResponsiveTable>
                          </div>
                        ) : (
                          <Alert variant="info" className="mb-0">
                            Aucune collection sélectionnée
                          </Alert>
                        )}
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>

                <Alert variant="warning" className="mt-3">
                  <i className="fas fa-info-circle me-2"></i>
                  Si aucun produit ni collection n'est sélectionné, la promotion s'appliquera à tous les produits.
                </Alert>
              </FormSection>
            )}

            {/* Étape 3: Confirmation */}
            {currentStep === 3 && (
              <FormSection>
                <h5 className="mb-4 text-primary">Confirmation</h5>
                <Card className="mb-4">
                  <Card.Body>
                    <h6 className="mb-3">Récapitulatif des informations</h6>

                    <Row>
                      <Col md={6}>
                        <div className="mb-3">
                          <strong>Nom:</strong> {formData.nom}
                        </div>
                        <div className="mb-3">
                          <strong>Code:</strong> {formData.code || 'Non renseigné'}
                        </div>
                        <div className="mb-3">
                          <strong>Description:</strong> {formData.description || 'Non renseignée'}
                        </div>
                        <div className="mb-3">
                          <strong>Type:</strong> {typesPromotion.find((t) => t.value === formData.type)?.label}
                        </div>
                        <div className="mb-3">
                          <strong>Valeur:</strong> {formData.valeur}
                          {formData.type === 'pourcentage' ? '%' : '€'}
                        </div>
                      </Col>

                      <Col md={6}>
                        <div className="mb-3">
                          <strong>Statut:</strong>
                          <Badge
                            bg={formData.statut === 'active' ? 'success' : formData.statut === 'inactive' ? 'danger' : 'warning'}
                            className="ms-2"
                          >
                            {statutsPromotion.find((s) => s.value === formData.statut)?.label}
                          </Badge>
                        </div>
                        <div className="mb-3">
                          <strong>Dates:</strong>
                          {formData.date_debut || formData.date_fin ? (
                            <span className="ms-2">
                              {formData.date_debut ? `Du ${formData.date_debut.toLocaleDateString()}` : ''}
                              {formData.date_fin ? ` au ${formData.date_fin.toLocaleDateString()}` : ''}
                            </span>
                          ) : (
                            'Permanente'
                          )}
                        </div>
                        <div className="mb-3">
                          <strong>Priorité:</strong> {formData.priorite}
                        </div>
                        <div className="mb-3">
                          <strong>Cumulable:</strong>
                          {formData.cumulable ? (
                            <i className="fas fa-check text-success ms-2"></i>
                          ) : (
                            <i className="fas fa-times text-danger ms-2"></i>
                          )}
                        </div>
                        <div className="mb-3">
                          <strong>Profils autorisés:</strong>
                          {formData.profils_remise.length > 0 ? (
                            formData.profils_remise.map((profil) => (
                              <Badge key={profil} bg="info" className="ms-2">
                                {profilsRemise.find((p) => p.value === profil)?.label}
                              </Badge>
                            ))
                          ) : (
                            <span className="ms-2">Tous les profils</span>
                          )}
                        </div>
                      </Col>
                    </Row>

                    <Row>
                      <Col md={6}>
                        <div className="mb-3">
                          <strong>Produits ciblés:</strong>
                          {formData.produits.length > 0 ? (
                            <ul className="mt-2">
                              {formData.produits.map((produitId) => {
                                const produit = allProduits.find((p) => p.id == produitId);
                                return <li key={produitId}>{produit ? produit.nom_produit : `Produit #${produitId}`}</li>;
                              })}
                            </ul>
                          ) : (
                            <span className="ms-2">Tous les produits</span>
                          )}
                        </div>
                      </Col>

                      <Col md={6}>
                        <div className="mb-3">
                          <strong>Collections ciblées:</strong>
                          {formData.collections.length > 0 ? (
                            <ul className="mt-2">
                              {formData.collections.map((collectionId) => {
                                const collection = allCollections.find((c) => c.id == collectionId);
                                return <li key={collectionId}>{collection ? collection.nom : `Collection #${collectionId}`}</li>;
                              })}
                            </ul>
                          ) : (
                            <span className="ms-2">Aucune collection spécifique</span>
                          )}
                        </div>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>

                <Form.Group className="mb-3">
                  <Form.Check type="checkbox" label="Je confirme que les informations ci-dessus sont exactes" required />
                </Form.Group>
              </FormSection>
            )}

            {/* Boutons de navigation */}
            <div className="d-flex justify-content-between mt-4 pt-3 border-top">
              {currentStep > 1 ? (
                <Button variant="outline-primary" onClick={prevStep} disabled={loading}>
                  <i className="fas fa-arrow-left me-2"></i>
                  Précédent
                </Button>
              ) : (
                <div></div>
              )}

              {currentStep < totalSteps ? (
                <Button variant="primary" onClick={nextStep} disabled={loading}>
                  Suivant
                  <i className="fas fa-arrow-right ms-2"></i>
                </Button>
              ) : (
                <Button variant="success" type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Spinner as="span" size="sm" animation="border" className="me-2" />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-check me-2"></i>
                      Confirmer et créer
                    </>
                  )}
                </Button>
              )}
            </div>
          </Form>
        </Card.Body>
      </PromotionCard>

      {/* Bouton pour voir la liste des promotions */}
      <div className="text-center mt-4">
        <Button
          variant="outline-primary"
          onClick={() => {
            fetchPromotions();
            setShowPromotionsList(true);
          }}
        >
          <i className="fas fa-list me-2"></i>
          Voir la liste des promotions
        </Button>
      </div>

      {/* Modal d'ajout de produit */}
      <Modal show={showAddProduit} onHide={() => setShowAddProduit(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Ajouter des produits</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loading ? (
            <div className="text-center my-4">
              <Spinner animation="border" variant="primary" />
              <p>Chargement des produits...</p>
            </div>
          ) : allProduits.length === 0 ? (
            <Alert variant="warning">Aucun produit disponible</Alert>
          ) : (
            <>
              <Form.Group className="mb-3">
                <Form.Label>Sélectionnez les produits à ajouter</Form.Label>
                <Form.Select
                  multiple
                  value={selectedProduits}
                  onChange={(e) => {
                    const options = e.target.options;
                    const selected = [];
                    for (let i = 0; i < options.length; i++) {
                      if (options[i].selected) {
                        selected.push(options[i].value);
                      }
                    }
                    setSelectedProduits(selected);
                  }}
                  size="10"
                >
                  {allProduits.map((produit) => (
                    <option key={produit.id} value={produit.id} disabled={formData.produits.includes(produit.id)}>
                      {produit.nom_produit} - {produit.prix_produit}€{formData.produits.includes(produit.id) && ' (déjà ajouté)'}
                    </option>
                  ))}
                </Form.Select>
                <Form.Text className="text-muted">Maintenez Ctrl pour sélectionner plusieurs produits</Form.Text>
              </Form.Group>

              {selectedProduits.length > 0 && <Alert variant="info">{selectedProduits.length} produit(s) sélectionné(s)</Alert>}
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddProduit(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={addProduits} disabled={selectedProduits.length === 0}>
            <i className="fas fa-plus me-2"></i>
            Ajouter les produits
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal d'ajout de collection */}
      <Modal show={showAddCollection} onHide={() => setShowAddCollection(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Ajouter des collections</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loading ? (
            <div className="text-center my-4">
              <Spinner animation="border" variant="primary" />
              <p>Chargement des collections...</p>
            </div>
          ) : allCollections.length === 0 ? (
            <Alert variant="warning">Aucune collection disponible</Alert>
          ) : (
            <>
              <Form.Group className="mb-3">
                <Form.Label>Sélectionnez les collections à ajouter</Form.Label>
                <Form.Select
                  multiple
                  value={selectedCollections}
                  onChange={(e) => {
                    const options = e.target.options;
                    const selected = [];
                    for (let i = 0; i < options.length; i++) {
                      if (options[i].selected) {
                        selected.push(options[i].value);
                      }
                    }
                    setSelectedCollections(selected);
                  }}
                  size="10"
                >
                  {allCollections.map((collection) => (
                    <option key={collection.id} value={collection.id} disabled={formData.collections.includes(collection.id)}>
                      {collection.nom}
                      {formData.collections.includes(collection.id) && ' (déjà ajoutée)'}
                    </option>
                  ))}
                </Form.Select>
                <Form.Text className="text-muted">Maintenez Ctrl pour sélectionner plusieurs collections</Form.Text>
              </Form.Group>

              {selectedCollections.length > 0 && <Alert variant="info">{selectedCollections.length} collection(s) sélectionnée(s)</Alert>}
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddCollection(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={addCollections} disabled={selectedCollections.length === 0}>
            <i className="fas fa-plus me-2"></i>
            Ajouter les collections
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal pour la liste des promotions */}
      <Modal show={showPromotionsList} onHide={() => setShowPromotionsList(false)} size="xl" scrollable>
        <Modal.Header closeButton>
          <Modal.Title>Liste des promotions</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {promotionsLoading ? (
            <div className="text-center my-4">
              <Spinner animation="border" variant="primary" />
              <p>Chargement des promotions...</p>
            </div>
          ) : promotions.length === 0 ? (
            <Alert variant="info">Aucune promotion disponible</Alert>
          ) : (
            <div className="table-responsive">
              <ResponsiveTable striped bordered hover>
                <thead>
                  <tr>
                    <th>Nom</th>
                    <th>Code</th>
                    <th>Type</th>
                    <th>Valeur</th>
                    <th>Statut</th>
                    <th>Dates</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {promotions.map((promotion) => (
                    <tr key={promotion.id}>
                      <td>{promotion.nom}</td>
                      <td>{promotion.code || '-'}</td>
                      <td>
                        {promotion.type === 'pourcentage' ? 'Pourcentage' : promotion.type === 'montant_fixe' ? 'Montant fixe' : 'Gratuit'}
                      </td>
                      <td>
                        {promotion.valeur}
                        {promotion.type === 'pourcentage' ? '%' : promotion.type === 'montant_fixe' ? '€' : ''}
                      </td>
                      <td>
                        <Badge bg={promotion.statut === 'active' ? 'success' : promotion.statut === 'inactive' ? 'danger' : 'warning'}>
                          {promotion.statut === 'active' ? 'Active' : promotion.statut === 'inactive' ? 'Inactive' : 'Programmée'}
                        </Badge>
                      </td>
                      <td>
                        {promotion.date_debut ? new Date(promotion.date_debut).toLocaleDateString() : '-'}
                        {' → '}
                        {promotion.date_fin ? new Date(promotion.date_fin).toLocaleDateString() : '-'}
                      </td>
                      <td>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => {
                            setFormData({
                              ...formData,
                              nom: promotion.nom,
                              code: promotion.code,
                              description: promotion.description,
                              type: promotion.type,
                              valeur: promotion.valeur,
                              statut: promotion.statut,
                              date_debut: promotion.date_debut ? new Date(promotion.date_debut) : null,
                              date_fin: promotion.date_fin ? new Date(promotion.date_fin) : null,
                              priorite: promotion.priorite || 10,
                              cumulable: promotion.cumulable || false,
                              produits: promotion.produits || [],
                              collections: promotion.collections || [],
                              profils_remise: promotion.profils_remise || []
                            });
                            setCurrentStep(1);
                            setShowPromotionsList(false);
                          }}
                        >
                          <i className="fas fa-edit"></i>
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </ResponsiveTable>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPromotionsList(false)}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>
    </ResponsiveContainer>
  );
};

export default GestionPromotions;
