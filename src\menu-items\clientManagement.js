// assets
import { IconUsers, IconSearch, IconDiscount, IconUserPlus } from '@tabler/icons-react';

// constant
const icons = {
  IconUsers,
  IconSearch,
  IconDiscount,
  IconUserPlus
};

// ==============================|| CLIENT MANAGEMENT MENU ITEMS ||============================== //

const clientManagement = {
  id: 'client-management',
  title: 'Gestion des Clients',
  type: 'group',
  children: [
    {
      id: 'clients',
      title: 'Clients',
      type: 'item',
      url: '/app/clients/list',
      icon: icons.IconUsers,
      breadcrumbs: false
    },
    {
      id: 'client-groups',
      title: 'Groupes de Clients',
      type: 'item',
      url: '/app/clients/groups',
      icon: icons.IconUserPlus,
      breadcrumbs: false
    },
    {
      id: 'client-discount-profiles',
      title: 'Profils de Remise',
      type: 'item',
      url: '/app/client-discount-profiles',
      icon: icons.IconDiscount,
      breadcrumbs: false
    }
  ]
};

export default clientManagement;
