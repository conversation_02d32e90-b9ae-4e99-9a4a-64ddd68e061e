// material-ui
import { useTheme } from '@mui/material/styles';
import { Typography, Box } from '@mui/material';

// ==============================|| LOGO ||============================== //

export default function Logo() {
  const theme = useTheme();

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      {/* Facebook Logo Blob */}
      <img
        src="blob:https://web.facebook.com/31e8b10e-8c3b-4de2-b71f-67a1ec1c95f3"
        alt="jihenLine Logo"
        style={{
          height: '32px',
          width: '32px',
          objectFit: 'contain',
          borderRadius: '4px'
        }}
        onError={(e) => {
          // Fallback to text logo if image fails to load
          e.target.style.display = 'none';
        }}
      />
      {/* Text Logo */}
      <Typography
        variant="h4"
        sx={{
          fontWeight: 700,
          color: theme.palette.primary.main,
          textDecoration: 'none',
          fontSize: '1.5rem',
          fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
        }}
      >
        jihenLine
      </Typography>
    </Box>
  );
}
