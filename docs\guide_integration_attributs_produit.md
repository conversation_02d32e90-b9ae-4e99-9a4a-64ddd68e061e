# Guide d'Intégration des Attributs de Produit dans la Page de Détails

Ce guide explique comment récupérer et afficher les informations d'un produit, ses attributs, ses images et ses variantes dans une page de détails de produit.

## Vue d'ensemble

Pour créer une page de détails de produit complète, vous devez récupérer plusieurs types d'informations :

1. **Informations de base du produit** (nom, description, prix)
2. **Attributs du produit** (caractéristiques, spécifications)
3. **Images du produit**
4. **Variantes du produit** (si disponibles)

## Étape 1 : Récupérer les informations de base du produit

```javascript
// Fonction pour récupérer les informations de base d'un produit
async function getProduit(produitId) {
  try {
    const response = await axios.get(`https://laravel-api.fly.dev/api/produits/${produitId}`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération du produit:', error);
    throw error;
  }
}
```

Cette fonction vous donne les informations de base comme :
- `id`
- `nom_produit`
- `description_produit`
- `prix_produit`
- `image_produit` (URL de l'image principale)

## Étape 2 : Récupérer les attributs du produit

```javascript
// Fonction pour récupérer les attributs d'un produit
async function getAttributsProduit(produitId) {
  try {
    const response = await axios.get(`https://laravel-api.fly.dev/api/produits/${produitId}/attributs`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des attributs:', error);
    throw error;
  }
}
```

Cette fonction retourne un tableau d'attributs avec leurs valeurs. Chaque attribut contient :
- `attribut.nom` - Le nom de l'attribut (ex: "Couleur")
- `attribut.groupe.nom` - Le groupe auquel appartient l'attribut (ex: "Caractéristiques générales")
- `valeur` - La valeur de l'attribut pour ce produit (ex: "Blanc")

## Étape 3 : Récupérer les images du produit

```javascript
// Fonction pour récupérer les images d'un produit
async function getImagesProduit(produitId) {
  try {
    const response = await axios.get('https://laravel-api.fly.dev/api/images/get', {
      params: {
        model_type: 'produit',
        model_id: produitId
      }
    });
    return response.data.images;
  } catch (error) {
    console.error('Erreur lors de la récupération des images:', error);
    throw error;
  }
}
```

Cette fonction retourne un tableau d'images avec :
- `direct_url` - L'URL directe de l'image à afficher
- `is_primary` - Indique si c'est l'image principale
- `alt_text` - Texte alternatif pour l'image
- `title` - Titre de l'image

## Étape 4 : Récupérer les variantes du produit (si nécessaire)

```javascript
// Fonction pour récupérer les variantes d'un produit
async function getVariantesProduit(produitId) {
  try {
    const response = await axios.get(`https://laravel-api.fly.dev/api/produits/${produitId}/variantes`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des variantes:', error);
    throw error;
  }
}
```

Cette fonction retourne les variantes disponibles pour le produit.

## Exemple complet : Charger toutes les données pour la page de détails

```javascript
// Fonction pour charger toutes les données nécessaires pour la page de détails
async function chargerPageDetailsProduit(produitId) {
  try {
    // Charger en parallèle toutes les données nécessaires
    const [produit, attributs, images, variantes] = await Promise.all([
      getProduit(produitId),
      getAttributsProduit(produitId),
      getImagesProduit(produitId),
      getVariantesProduit(produitId)
    ]);
    
    // Retourner toutes les données dans un seul objet
    return {
      produit,
      attributs,
      images,
      variantes
    };
  } catch (error) {
    console.error('Erreur lors du chargement des données du produit:', error);
    throw error;
  }
}
```

## Affichage des attributs par groupe

Pour afficher les attributs organisés par groupe, vous pouvez utiliser ce code :

```javascript
// Fonction pour organiser les attributs par groupe
function organiserAttributsParGroupe(attributs) {
  const groupes = {};
  
  attributs.forEach(attr => {
    const nomGroupe = attr.attribut.groupe.nom;
    
    if (!groupes[nomGroupe]) {
      groupes[nomGroupe] = [];
    }
    
    groupes[nomGroupe].push({
      nom: attr.attribut.nom,
      valeur: attr.valeur
    });
  });
  
  return groupes;
}
```

## Exemple d'utilisation dans un composant React

```jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const PageDetailsProduit = ({ produitId }) => {
  const [donnees, setDonnees] = useState({
    produit: null,
    attributs: [],
    images: [],
    variantes: []
  });
  const [chargement, setChargement] = useState(true);
  const [erreur, setErreur] = useState(null);
  
  useEffect(() => {
    async function chargerDonnees() {
      try {
        setChargement(true);
        const resultat = await chargerPageDetailsProduit(produitId);
        setDonnees(resultat);
        setChargement(false);
      } catch (err) {
        setErreur('Erreur lors du chargement des données');
        setChargement(false);
        console.error(err);
      }
    }
    
    chargerDonnees();
  }, [produitId]);
  
  // Organiser les attributs par groupe
  const attributsParGroupe = donnees.attributs.length > 0 
    ? organiserAttributsParGroupe(donnees.attributs) 
    : {};
  
  if (chargement) return <div>Chargement en cours...</div>;
  if (erreur) return <div>{erreur}</div>;
  if (!donnees.produit) return <div>Produit non trouvé</div>;
  
  return (
    <div className="page-details-produit">
      <h1>{donnees.produit.nom_produit}</h1>
      
      {/* Galerie d'images */}
      <div className="galerie-images">
        {donnees.images.map(image => (
          <img 
            key={image.id} 
            src={image.direct_url} 
            alt={image.alt_text || donnees.produit.nom_produit} 
            className={image.is_primary ? 'image-principale' : 'image-secondaire'}
          />
        ))}
      </div>
      
      {/* Informations de base */}
      <div className="info-base">
        <p className="prix">{donnees.produit.prix_produit} €</p>
        <p className="description">{donnees.produit.description_produit}</p>
      </div>
      
      {/* Variantes */}
      {donnees.variantes.length > 0 && (
        <div className="variantes">
          <h2>Variantes disponibles</h2>
          <div className="liste-variantes">
            {donnees.variantes.map(variante => (
              <div key={variante.id} className="variante">
                {/* Afficher les détails de la variante */}
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Attributs par groupe */}
      <div className="attributs">
        <h2>Caractéristiques du produit</h2>
        
        {Object.entries(attributsParGroupe).map(([nomGroupe, attributs]) => (
          <div key={nomGroupe} className="groupe-attributs">
            <h3>{nomGroupe}</h3>
            <table>
              <tbody>
                {attributs.map((attr, index) => (
                  <tr key={index}>
                    <td className="nom-attribut">{attr.nom}</td>
                    <td className="valeur-attribut">
                      {typeof attr.valeur === 'boolean' 
                        ? (attr.valeur ? 'Oui' : 'Non') 
                        : attr.valeur}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PageDetailsProduit;
```

## Points importants à retenir

1. **Chargement parallèle** : Utilisez `Promise.all` pour charger toutes les données en parallèle et améliorer les performances.

2. **Gestion des erreurs** : Assurez-vous de gérer correctement les erreurs pour chaque appel API.

3. **Attributs booléens** : Pour les attributs de type booléen, convertissez `true`/`false` en "Oui"/"Non" pour l'affichage.

4. **Images principales** : Utilisez la propriété `is_primary` pour identifier l'image principale à afficher en premier.

5. **Organisation par groupe** : Affichez les attributs organisés par groupe pour une meilleure lisibilité.

Ce guide devrait vous aider à intégrer facilement les attributs de produit dans votre page de détails. Si vous avez des questions supplémentaires, n'hésitez pas à demander de l'aide.
