const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Carousels
export async function fetchCarousels() {
  const res = await fetch(`${API_URL}/carousels`);
  if (!res.ok) throw new Error('Error loading carousels');

  const data = await res.json();
  return Array.isArray(data) ? data.map(convertCarouselFromApi) : [];
}

export async function fetchCarouselById(id) {
  const res = await fetch(`${API_URL}/carousels/${id}`);
  if (!res.ok) throw new Error('Error loading carousel');

  const data = await res.json();
  return convertCarouselFromApi(data);
}

export async function createCarousel(data) {
  // Convert field names to match API expectations
  const apiData = {
    nom: data.name,
    description: data.description,
    actif: data.is_active
    // Add any other fields needed by the API
  };

  const res = await fetch(`${API_URL}/carousels`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Error creating carousel');

  const responseData = await res.json();
  return convertCarouselFromApi(responseData);
}

export async function updateCarousel(id, data) {
  // Convert field names to match API expectations
  const apiData = {
    nom: data.name,
    description: data.description,
    actif: data.is_active
    // Add any other fields needed by the API
  };

  const res = await fetch(`${API_URL}/carousels/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Error updating carousel');

  const responseData = await res.json();
  return convertCarouselFromApi(responseData);
}

export async function deleteCarousel(id) {
  const res = await fetch(`${API_URL}/carousels/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting carousel');
  return res.json();
}

// Helper function to convert carousel from API format to our internal format
function convertCarouselFromApi(apiCarousel) {
  if (!apiCarousel) return null;

  return {
    id: apiCarousel.id,
    name: apiCarousel.nom,
    description: apiCarousel.description || '',
    is_active: apiCarousel.actif,
    ordre: apiCarousel.ordre,
    created_at: apiCarousel.created_at,
    updated_at: apiCarousel.updated_at,
    slides: Array.isArray(apiCarousel.slides) ? apiCarousel.slides.map(convertSlideFromApi) : [],
    slides_count: Array.isArray(apiCarousel.slides) ? apiCarousel.slides.length : 0,
    // Keep original data for reference
    _original: apiCarousel
  };
}

// Carousel Slides
export async function fetchSlides(carouselId) {
  const res = await fetch(`${API_URL}/carousels/${carouselId}/slides`);
  if (!res.ok) throw new Error('Error loading slides');

  const data = await res.json();
  return Array.isArray(data) ? data.map(convertSlideFromApi) : [];
}

export async function fetchSlideById(id) {
  const res = await fetch(`${API_URL}/slides/${id}`);
  if (!res.ok) throw new Error('Error loading slide');

  const data = await res.json();
  return convertSlideFromApi(data);
}

export async function createSlide(carouselId, data) {
  // Convert field names to match API expectations
  const apiData = {
    carousel_id: carouselId,
    titre: data.title,
    description: data.content,
    bouton_texte: data.button_text,
    bouton_lien: data.button_link,
    actif: data.is_active,
    ordre: data.ordre || 0
  };

  const res = await fetch(`${API_URL}/carousels/${carouselId}/slides`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Error creating slide');

  const responseData = await res.json();
  return convertSlideFromApi(responseData);
}

export async function updateSlide(id, data) {
  // Convert field names to match API expectations
  const apiData = {
    titre: data.title,
    description: data.content,
    bouton_texte: data.button_text,
    bouton_lien: data.button_link,
    actif: data.is_active,
    ordre: data.ordre
  };

  const res = await fetch(`${API_URL}/slides/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Error updating slide');

  const responseData = await res.json();
  return convertSlideFromApi(responseData);
}

export async function deleteSlide(id) {
  const res = await fetch(`${API_URL}/slides/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting slide');
  return res.json();
}

export async function reorderSlides(carouselId, slideIds) {
  const res = await fetch(`${API_URL}/carousels/${carouselId}/slides/reorder`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ slide_ids: slideIds })
  });
  if (!res.ok) throw new Error('Error reordering slides');
  return res.json();
}

// Helper function to convert slide from API format to our internal format
function convertSlideFromApi(apiSlide) {
  if (!apiSlide) return null;

  return {
    id: apiSlide.id,
    carousel_id: apiSlide.carousel_id,
    title: apiSlide.titre,
    content: apiSlide.description || '',
    button_text: apiSlide.bouton_texte || '',
    button_link: apiSlide.bouton_lien || '',
    is_active: apiSlide.actif,
    ordre: apiSlide.ordre,
    created_at: apiSlide.created_at,
    updated_at: apiSlide.updated_at,
    primary_image_url: apiSlide.primary_image_url,
    images: apiSlide.images || [],
    // Keep original data for reference
    _original: apiSlide
  };
}

// Featured Content
export async function fetchFeaturedContent() {
  const res = await fetch(`${API_URL}/featured-content`);
  if (!res.ok) throw new Error('Error loading featured content');
  return res.json();
}

export async function updateFeaturedContent(data) {
  const res = await fetch(`${API_URL}/featured-content`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating featured content');
  return res.json();
}

// Homepage Sections
export async function fetchHomepageSections() {
  const res = await fetch(`${API_URL}/homepage-sections`);
  if (!res.ok) throw new Error('Error loading homepage sections');
  return res.json();
}

export async function fetchHomepageSectionById(id) {
  const res = await fetch(`${API_URL}/homepage-sections/${id}`);
  if (!res.ok) throw new Error('Error loading homepage section');
  return res.json();
}

export async function createHomepageSection(data) {
  const res = await fetch(`${API_URL}/homepage-sections`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error creating homepage section');
  return res.json();
}

export async function updateHomepageSection(id, data) {
  const res = await fetch(`${API_URL}/homepage-sections/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating homepage section');
  return res.json();
}

export async function deleteHomepageSection(id) {
  const res = await fetch(`${API_URL}/homepage-sections/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting homepage section');
  return res.json();
}

export async function reorderHomepageSections(sectionIds) {
  const res = await fetch(`${API_URL}/homepage-sections/reorder`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ section_ids: sectionIds })
  });
  if (!res.ok) throw new Error('Error reordering homepage sections');
  return res.json();
}
