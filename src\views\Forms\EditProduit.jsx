import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  But<PERSON>,
  Row,
  Col,
  Al<PERSON>,
  Spinner,
  Modal,
  ListGroup,
  Badge,
  Tab,
  Nav,
  Table,
  InputGroup
} from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import ProductStep1 from './ProductStep1';
import ProductStep2 from './ProductStep2';
import ProductStep3 from './ProductStep3';
import ProductStep4 from './ProductStep4';
import ProductStepVariants from './ProductStepVariants';
import { fetchCategories, fetchSousCategories, fetchSousSousCategories } from '../../services/categoryService';
import { fetchProductAttributs, fetchSousSousCategorieAttributs, fetchProductVariantes } from '../../services/attributService';

const EditProduit = ({ product, onClose, onSuccess }) => {
  const API_URL = 'https://laravel-api.fly.dev/api';
  const [formData, setFormData] = useState({ ...product });
  const [categories, setCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [sousCategoriesLoading, setSousCategoriesLoading] = useState(false);
  const [sousSousCategoriesLoading, setSousSousCategoriesLoading] = useState(false);
  const [marques, setMarques] = useState([]);
  const [attributs, setAttributs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedAttributs, setSelectedAttributs] = useState(product.attributs || []);
  const [attributValues, setAttributValues] = useState(product.attributValues || {});
  const [images, setImages] = useState(product.images ? [...product.images] : []); // Existing images + new uploads
  const [primaryImageIndex, setPrimaryImageIndex] = useState(() => {
    if (product.images && product.images.length > 0) {
      const idx = product.images.findIndex((img) => img.is_primary);
      return idx >= 0 ? idx : 0;
    }
    return 0;
  });
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [variantes, setVariantes] = useState(product.variantes || []);
  const [showVarianteModal, setShowVarianteModal] = useState(false);
  const [newVariante, setNewVariante] = useState({ sku: '', prix_supplement: 0, quantite: 0, image: null, valeurs: {} });
  const [varianteImagePreview, setVarianteImagePreview] = useState(null);
  const totalSteps = 5;

  // Fetch attributs and variantes for product and sous-sous-categorie
  useEffect(() => {
    const fetchAttributsAndVariantes = async () => {
      try {
        // Fetch attributs for product
        if (product.id) {
          const prodAttrRes = await fetchProductAttributs(product.id);
          setAttributs(prodAttrRes.data || prodAttrRes || []);
        }
        // Fetch attributs for sous-sous-categorie if available
        const sousSousCatId = formData.sous_sous_categorie_id || product.sous_sous_categorie_id;
        if (sousSousCatId) {
          const sousSousAttrRes = await fetchSousSousCategorieAttributs(sousSousCatId);
          // Optionally merge with product attributs if needed
          setAttributs((prev) =>
            Array.isArray(prev)
              ? [...prev, ...(sousSousAttrRes.data || sousSousAttrRes || [])]
              : sousSousAttrRes.data || sousSousAttrRes || []
          );
        }
        // Fetch variants for product
        if (product.id) {
          const variantesRes = await fetchProductVariantes(product.id);
          setVariantes(variantesRes.data || variantesRes || []);
        }
      } catch (err) {
        // Optionally set error
      }
    };
    fetchAttributsAndVariantes();
  }, [product.id, formData.sous_sous_categorie_id]);

  // Automatically determine and set the correct category and sous-category based on sous_sous_categorie_id
  useEffect(() => {
    const preselectCategoryHierarchy = async () => {
      if (product.sous_sous_categorie_id && categories.length > 0) {
        // Fetch all sous-sous-categories for the current sous_categorie (or all if needed)
        let allSousSousCats = [];
        try {
          // Try to fetch all sous-sous-categories for all sous-categories (for robustness)
          // If you have an endpoint for all, use it; otherwise, fetch for each sous-categorie
          // Here, we assume you can fetch for each sous-categorie in categories
          for (const cat of categories) {
            const sousCatsData = await fetchSousCategories(cat.id);
            const sousCats = sousCatsData.data || sousCatsData || [];
            for (const sousCat of sousCats) {
              const sousSousCatsData = await fetchSousSousCategories(sousCat.id);
              const sousSousCats = sousSousCatsData.data || sousSousCatsData || [];
              allSousSousCats = allSousSousCats.concat(sousSousCats);
            }
          }
        } catch (err) {
          // fallback: leave allSousSousCats empty
        }
        const sousSousCat = allSousSousCats.find((ssc) => ssc.id === product.sous_sous_categorie_id);
        if (sousSousCat) {
          const sous_categorie_id = sousSousCat.sous_categorie_id;
          // Now find the sous-categorie and its categorie_id
          let matchedSousCat = null;
          let matchedCategorieId = null;
          for (const cat of categories) {
            const sousCatsData = await fetchSousCategories(cat.id);
            const sousCats = sousCatsData.data || sousCatsData || [];
            const foundSousCat = sousCats.find((sc) => sc.id === sous_categorie_id);
            if (foundSousCat) {
              matchedSousCat = foundSousCat;
              matchedCategorieId = cat.id;
              break;
            }
          }
          if (matchedSousCat && matchedCategorieId) {
            setFormData((prev) => ({
              ...prev,
              categorie_id: matchedCategorieId,
              sous_categorie_id: matchedSousCat.id,
              sous_sous_categorie_id: product.sous_sous_categorie_id
            }));
          }
        }
      }
    };
    preselectCategoryHierarchy();
  }, [categories, product.sous_sous_categorie_id]);

  // Fetch sousCategories when the selected category changes
  useEffect(() => {
    const fetchSousCats = async () => {
      const catId = formData.categorie_id || product.categorie_id;
      if (catId) {
        try {
          setSousCategoriesLoading(true);
          const sousCatsData = await fetchSousCategories(catId);
          setSousCategories(sousCatsData.data || sousCatsData || []);
          setSousCategoriesLoading(false);
        } catch (err) {
          setSousCategories([]);
        }
      } else {
        setSousCategories([]);
      }
    };
    fetchSousCats();
  }, [formData.categorie_id, product.categorie_id]);

  // Fetch sousSousCategories when the selected sous-categorie changes
  useEffect(() => {
    const fetchSousSousCats = async () => {
      const sousCatId = formData.sous_categorie_id || product.sous_categorie_id;
      if (sousCatId) {
        try {
          setSousSousCategoriesLoading(true);
          const sousSousCatsData = await fetchSousSousCategories(sousCatId);
          setSousSousCategories(sousSousCatsData.data || sousSousCatsData || []);
          setSousSousCategoriesLoading(false);
        } catch (err) {
          setSousSousCategories([]);
        }
      } else {
        setSousSousCategories([]);
      }
    };
    fetchSousSousCats();
  }, [formData.sous_categorie_id, product.sous_categorie_id]);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        // Fetch marques
        const marquesRes = await fetch(`${API_URL}/marques`);
        const marquesData = await marquesRes.json();
        setMarques(marquesData.data || marquesData || []);

        // Fetch categories using service
        setCategoriesLoading(true);
        const categoriesData = await fetchCategories();
        setCategories(categoriesData.data || categoriesData || []);
        setCategoriesLoading(false);
      } catch (err) {
        setError('Erreur de chargement des données initiales');
      } finally {
        setLoading(false);
      }
    };
    fetchInitialData();
  }, [product]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Handle multiple image selection (append new images)
  const handleImagesChange = (e) => {
    const files = Array.from(e.target.files);
    // Only add files that are not already present (avoid duplicates by name)
    const newImages = files.filter((f) => !images.some((img) => (img instanceof File ? img.name : img.id) === f.name));
    setImages((prev) => [...prev, ...newImages]);
    // If no primary selected, set the first as primary
    if (images.length === 0 && newImages.length > 0) setPrimaryImageIndex(0);
  };

  // Set a specific image as primary
  const handleSetPrimaryImage = (index) => {
    setPrimaryImageIndex(index);
  };

  // Remove image
  const handleRemoveImage = (idx) => {
    setImages((prev) => prev.filter((_, i) => i !== idx));
    if (primaryImageIndex === idx) setPrimaryImageIndex(0);
    else if (primaryImageIndex > idx) setPrimaryImageIndex(primaryImageIndex - 1);
  };

  const submitEditForm = async () => {
    setShowConfirmModal(false);
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      // 1. Update product info
      const formDataToSend = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formDataToSend.append(key, value);
        }
      });
      const response = await fetch(`${API_URL}/produits/${product.id}`, {
        method: 'PUT',
        body: formDataToSend
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la mise à jour du produit');
      }

      // 2. Upload new images if any (Files only)
      const existingImages = images.filter((img) => !(img instanceof File));
      const newImages = images.filter((img) => img instanceof File);
      if (newImages.length > 0) {
        const imagesFormData = new FormData();
        newImages.forEach((img, idx) => {
          imagesFormData.append('images[]', img);
        });
        imagesFormData.append('model_type', 'produit');
        imagesFormData.append('model_id', product.id);
        // Mark the primary image index among ALL images
        imagesFormData.append('primary_index', primaryImageIndex);
        const imgResponse = await fetch(`${API_URL}/images/upload-multiple`, {
          method: 'POST',
          body: imagesFormData
        });
        if (!imgResponse.ok) {
          const imgError = await imgResponse.json();
          throw new Error(imgError.message || "Erreur lors de l'upload des images");
        }
      } else if (existingImages.length > 0) {
        // If only existing images, update primary via PATCH
        await fetch(`${API_URL}/images/set-primary`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            model_type: 'produit',
            model_id: product.id,
            image_id: existingImages[primaryImageIndex]?.id
          })
        });
      }

      setSuccess(true);
      if (onSuccess) onSuccess();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const nextStep = () => {
    setCurrentStep(currentStep + 1);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (currentStep === totalSteps) {
      // Prevent submission if no image is selected
      if (images.length === 0) {
        setError('Veuillez sélectionner au moins une image pour le produit.');
        return;
      }
      submitEditForm();
    } else {
      nextStep();
    }
  };

  return (
    <Container>
      <Card className="mt-4">
        <Card.Body>
          <Form onSubmit={handleSubmit}>
            {/* Barre de progression */}
            <div className="mb-4">
              <div className="d-flex align-items-center">
                {[1, 2, 3, 4, 5].map((step) => (
                  <React.Fragment key={step}>
                    <div
                      className={`d-flex flex-column align-items-center ${currentStep > step ? 'text-primary' : currentStep === step ? 'font-weight-bold text-primary' : 'text-muted'}`}
                    >
                      <div
                        className={`rounded-circle d-flex align-items-center justify-content-center mb-2 ${currentStep > step ? 'bg-primary text-white' : currentStep === step ? 'bg-primary text-white' : 'bg-light'}`}
                        style={{ width: '40px', height: '40px' }}
                      >
                        {currentStep > step ? <i className="fas fa-check"></i> : step}
                      </div>
                      <span className="small">
                        {step === 1 && 'Informations'}
                        {step === 2 && 'Catégorie & Variantes'}
                        {step === 3 && 'Attributs'}
                        {step === 4 && 'Image'}
                        {step === 5 && 'Confirmation'}
                      </span>
                    </div>
                    {step < 5 && (
                      <div className={`flex-grow-1 mx-2 ${currentStep > step ? 'bg-primary' : 'bg-light'}`} style={{ height: '2px' }}></div>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>

            {currentStep === 1 && <ProductStep1 formData={formData} setFormData={setFormData} error={error} />}
            {currentStep === 2 && (
              <ProductStep2
                formData={formData}
                setFormData={setFormData}
                brands={marques}
                categories={categories}
                sousCategories={sousCategories}
                sousSousCategories={sousSousCategories}
                categoriesLoading={categoriesLoading}
                sousCategoriesLoading={sousCategoriesLoading}
                sousSousCategoriesLoading={sousSousCategoriesLoading}
                error={error}
              />
            )}
            {currentStep === 3 && (
              <ProductStep3
                formData={formData}
                setFormData={setFormData}
                attributFields={attributs}
                attributValues={attributValues}
                setAttributValues={setAttributValues}
                error={error}
              />
            )}
            {currentStep === 4 && (
              <div>
                <Form.Group controlId="productImages">
                  <Form.Label>
                    Images du produit <span style={{ color: 'red' }}>*</span>
                  </Form.Label>
                  <Form.Control type="file" multiple accept="image/*" onChange={handleImagesChange} />
                  <Form.Text className="text-muted">
                    Veuillez sélectionner au moins une image. Cliquez sur une image pour la définir comme principale.
                  </Form.Text>
                  <div className="d-flex mt-2 flex-wrap">
                    {images.map((img, idx) => (
                      <div
                        key={idx}
                        className={`m-2 border ${idx === primaryImageIndex ? 'border-primary' : ''}`}
                        style={{ cursor: 'pointer', width: 80, height: 80, position: 'relative' }}
                      >
                        <img
                          src={img instanceof File ? URL.createObjectURL(img) : img.url}
                          alt={img.name || img.filename || 'image'}
                          style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 4 }}
                          onClick={() => handleSetPrimaryImage(idx)}
                        />
                        {idx === primaryImageIndex && (
                          <Badge bg="primary" style={{ position: 'absolute', top: 0, left: 0 }}>
                            Principale
                          </Badge>
                        )}
                        <Button
                          variant="danger"
                          size="sm"
                          style={{ position: 'absolute', top: 0, right: 0, borderRadius: '50%' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveImage(idx);
                          }}
                        >
                          &times;
                        </Button>
                      </div>
                    ))}
                  </div>
                </Form.Group>
              </div>
            )}
            {currentStep === 5 && (
              <ProductStepVariants
                variantes={variantes}
                setVariantes={setVariantes}
                newVariante={newVariante}
                setNewVariante={setNewVariante}
                showVarianteModal={showVarianteModal}
                setShowVarianteModal={setShowVarianteModal}
                handleVarianteImageChange={() => {}}
                varianteImagePreview={varianteImagePreview}
                setVarianteImagePreview={setVarianteImagePreview}
                attributs={attributs}
                handleVarianteAttributChange={() => {}}
                addVariante={() => {}}
                removeVariante={() => {}}
                error={error}
              />
            )}

            <div className="d-flex justify-content-between mt-4">
              {currentStep > 1 && (
                <Button variant="secondary" onClick={prevStep}>
                  Précédent
                </Button>
              )}
              {currentStep < totalSteps && (
                <Button variant="primary" onClick={nextStep}>
                  Suivant
                </Button>
              )}
              {currentStep === totalSteps && (
                <Button variant="primary" type="submit">
                  Enregistrer les modifications
                </Button>
              )}
            </div>
          </Form>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default EditProduit;
