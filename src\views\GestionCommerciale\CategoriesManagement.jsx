import React, { useEffect, useState } from 'react';
import {
  fetchCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  fetchAllSousCategories,
  createSousCategorie,
  updateSousCategorie,
  deleteSousCategorie
} from '../../services/categoryService';
import { Table, Button, Form, Alert, Spinner, Container, Row, Col, Card, Tabs, Tab, Badge, Breadcrumb } from 'react-bootstrap';
import SousSousCategories from './SousSousCategories';
import FeaturedCategories from './FeaturedCategories';
import ImageManager from './ImageManager';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaHome, FaLayerGroup, FaStar } from 'react-icons/fa';

const initialCategoryForm = { nom: '', description: '', image: '' };
const initialSousCategoryForm = { nom: '', description: '', categorie_id: '' };

export default function CategoriesManagement() {
  // Categories
  const [categories, setCategories] = useState([]);
  const [catLoading, setCatLoading] = useState(true);
  const [catError, setCatError] = useState('');
  const [catForm, setCatForm] = useState(initialCategoryForm);
  const [catEditingId, setCatEditingId] = useState(null);
  const [catSubmitting, setCatSubmitting] = useState(false);

  // Sous-categories
  const [sousCategories, setSousCategories] = useState([]);
  const [sousCatLoading, setSousCatLoading] = useState(true);
  const [sousCatError, setSousCatError] = useState('');
  const [sousCatForm, setSousCatForm] = useState(initialSousCategoryForm);
  const [sousCatEditingId, setSousCatEditingId] = useState(null);
  const [sousCatSubmitting, setSousCatSubmitting] = useState(false);

  const [tab, setTab] = useState('categories');

  // Load data
  const loadCategories = async () => {
    setCatLoading(true);
    setCatError('');
    try {
      const data = await fetchCategories();
      setCategories(data);
    } catch (e) {
      setCatError(e.message);
    }
    setCatLoading(false);
  };
  const loadSousCategories = async () => {
    setSousCatLoading(true);
    setSousCatError('');
    try {
      const data = await fetchAllSousCategories();
      setSousCategories(data);
    } catch (e) {
      setSousCatError(e.message);
    }
    setSousCatLoading(false);
  };

  useEffect(() => {
    loadCategories();
    loadSousCategories();
  }, []);

  // Category handlers
  const handleCatChange = (e) => setCatForm({ ...catForm, [e.target.name]: e.target.value });
  const handleCatSubmit = async (e) => {
    e.preventDefault();
    setCatSubmitting(true);
    setCatError('');
    try {
      if (catEditingId) {
        await updateCategory(catEditingId, catForm);
      } else {
        await createCategory(catForm);
      }
      setCatForm(initialCategoryForm);
      setCatEditingId(null);
      loadCategories();
    } catch (e) {
      setCatError(e.message);
    }
    setCatSubmitting(false);
  };
  const handleCatEdit = (cat) => {
    setCatForm({
      nom: cat.nom || cat.nom_categorie,
      description: cat.description || cat.description_categorie,
      image: cat.image || cat.image_categorie
    });
    setCatEditingId(cat.id);
  };
  const handleCatDelete = async (id) => {
    if (!window.confirm('Supprimer cette catégorie ?')) return;
    setCatError('');
    try {
      await deleteCategory(id);
      loadCategories();
      loadSousCategories(); // update sous-categories if parent deleted
    } catch (e) {
      setCatError(e.message);
    }
  };

  // Sous-categorie handlers
  const handleSousCatChange = (e) => setSousCatForm({ ...sousCatForm, [e.target.name]: e.target.value });
  const handleSousCatSubmit = async (e) => {
    e.preventDefault();
    setSousCatSubmitting(true);
    setSousCatError('');
    try {
      if (sousCatEditingId) {
        await updateSousCategorie(sousCatEditingId, sousCatForm);
      } else {
        await createSousCategorie(sousCatForm);
      }
      setSousCatForm(initialSousCategoryForm);
      setSousCatEditingId(null);
      loadSousCategories();
    } catch (e) {
      setSousCatError(e.message);
    }
    setSousCatSubmitting(false);
  };
  const handleSousCatEdit = (sc) => {
    setSousCatForm({
      nom: sc.nom || sc.nom_sous_categorie,
      description: sc.description || sc.description_sous_categorie,
      categorie_id: sc.categorie_id
    });
    setSousCatEditingId(sc.id);
  };
  const handleSousCatDelete = async (id) => {
    if (!window.confirm('Supprimer cette sous-catégorie ?')) return;
    setSousCatError('');
    try {
      await deleteSousCategorie(id);
      loadSousCategories();
    } catch (e) {
      setSousCatError(e.message);
    }
  };

  return (
    <Container fluid className="py-4">
      {/* Header and Breadcrumb */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaLayerGroup className="me-2" />
          Gestion des Catégories
        </h2>
        <Breadcrumb>
          <Breadcrumb.Item href="/dashboard">
            <FaHome size={14} className="me-1" /> Accueil
          </Breadcrumb.Item>
          <Breadcrumb.Item active>Gestion des Catégories</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={tab} onSelect={setTab} className="mb-0 nav-tabs-custom" fill>
            <Tab
              eventKey="categories"
              title={
                <span>
                  <Badge bg="primary" pill className="me-2">
                    {categories.length}
                  </Badge>
                  Catégories
                </span>
              }
            />
            <Tab
              eventKey="sous-categories"
              title={
                <span>
                  <Badge bg="success" pill className="me-2">
                    {sousCategories.length}
                  </Badge>
                  Sous-catégories
                </span>
              }
            />
            <Tab eventKey="sous-sous-categories" title="Sous-sous-catégories" />
            <Tab
              eventKey="featured-categories"
              title={
                <span>
                  <FaStar className="me-2 text-warning" />
                  Catégories Mises en Avant
                </span>
              }
            />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Custom CSS for tabs */}
      <style jsx="true">{`
        .nav-tabs-custom .nav-link {
          color: #495057;
          font-weight: 500;
          padding: 1rem 1.5rem;
          border-radius: 0;
          border: none;
          border-bottom: 3px solid transparent;
        }
        .nav-tabs-custom .nav-link.active {
          color: #2196f3;
          background: transparent;
          border-bottom: 3px solid #2196f3;
        }
        .nav-tabs-custom .nav-link:hover:not(.active) {
          border-bottom: 3px solid #e9ecef;
        }
      `}</style>
      {tab === 'categories' && (
        <>
          {catError && <Alert variant="danger">{catError}</Alert>}
          <Card className="mb-4 shadow-sm border-0">
            <Card.Header className="bg-white py-3">
              <h5 className="mb-0 fw-bold">{catEditingId ? 'Modifier une catégorie' : 'Ajouter une nouvelle catégorie'}</h5>
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleCatSubmit}>
                <Row className="g-3">
                  <Col xs={12} md={4}>
                    <Form.Group controlId="categoryNom">
                      <Form.Label className="fw-medium">Nom de la catégorie</Form.Label>
                      <Form.Control
                        name="nom"
                        value={catForm.nom || ''}
                        onChange={handleCatChange}
                        placeholder="Nom de la catégorie"
                        required
                        disabled={catSubmitting}
                        className="rounded-3 border-2"
                      />
                      <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="categoryDescription">
                      <Form.Label className="fw-medium">Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="description"
                        value={catForm.description || ''}
                        onChange={handleCatChange}
                        placeholder="Description détaillée de la catégorie"
                        disabled={catSubmitting}
                        className="rounded-3 border-2"
                      />
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="categoryImage">
                      <Form.Label className="fw-medium">Image (URL)</Form.Label>
                      <Form.Control
                        name="image"
                        value={catForm.image || ''}
                        onChange={handleCatChange}
                        placeholder="ex: https://..."
                        disabled={catSubmitting}
                        className="rounded-3 border-2"
                      />
                      <Form.Text className="text-muted">URL de l'image représentant la catégorie.</Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
                <div className="d-flex gap-2 mt-4 justify-content-end">
                  {catEditingId && (
                    <Button
                      variant="outline-secondary"
                      type="button"
                      onClick={() => {
                        setCatForm(initialCategoryForm);
                        setCatEditingId(null);
                      }}
                      className="px-4"
                    >
                      <i className="bi bi-x-circle me-2"></i>
                      Annuler
                    </Button>
                  )}
                  <Button type="submit" variant={catEditingId ? 'warning' : 'primary'} disabled={catSubmitting} className="px-4">
                    {catSubmitting ? (
                      <>
                        <Spinner size="sm" animation="border" className="me-2" />
                        Traitement...
                      </>
                    ) : catEditingId ? (
                      <>
                        <FaPencilAlt className="me-2" />
                        Mettre à jour
                      </>
                    ) : (
                      <>
                        <FaPlus className="me-2" />
                        Ajouter
                      </>
                    )}
                  </Button>
                </div>
              </Form>
              {/* Image management for selected category */}
              {catEditingId && (
                <div className="mt-4 pt-3 border-top">
                  <h6 className="mb-3 text-muted">Gestion des images</h6>
                  <ImageManager modelType="categorie" modelId={catEditingId} />
                </div>
              )}
            </Card.Body>
          </Card>
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des catégories</h5>
              <div className="text-muted small">
                {categories.length} catégorie{categories.length !== 1 ? 's' : ''} au total
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {catLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Chargement des catégories...</p>
                </div>
              ) : categories.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <i className="bi bi-folder text-muted" style={{ fontSize: '3rem' }}></i>
                  </div>
                  <p className="text-muted">Aucune catégorie trouvée.</p>
                  <Button variant="primary" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>
                    <FaPlus className="me-2" />
                    Créer une catégorie
                  </Button>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table hover responsive className="align-middle mb-0">
                    <thead>
                      <tr className="bg-light">
                        <th className="ps-3" style={{ width: '60px' }}>
                          ID
                        </th>
                        <th style={{ width: '25%' }}>Nom</th>
                        <th style={{ width: '35%' }}>Description</th>
                        <th style={{ width: '20%' }}>Image</th>
                        <th className="text-end pe-3" style={{ width: '20%' }}>
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {categories.map((cat) => (
                        <tr key={cat.id} className="border-bottom">
                          <td className="ps-3 fw-medium">{cat.id}</td>
                          <td>
                            <div className="d-flex align-items-center">
                              <div className="color-dot bg-primary me-2"></div>
                              <span className="fw-medium">{cat.nom || cat.nom_categorie}</span>
                            </div>
                          </td>
                          <td>
                            <div className="text-truncate" style={{ maxWidth: '300px' }}>
                              {cat.description || cat.description_categorie || (
                                <span className="text-muted fst-italic">Aucune description</span>
                              )}
                            </div>
                          </td>
                          <td>
                            {cat.image || cat.image_categorie ? (
                              <div className="d-flex align-items-center">
                                <div className="image-thumbnail me-2">
                                  <img
                                    src={cat.image || cat.image_categorie}
                                    alt={cat.nom || cat.nom_categorie}
                                    onError={(e) => {
                                      e.target.src = 'https://via.placeholder.com/40';
                                    }}
                                    width="40"
                                    height="40"
                                    className="rounded"
                                  />
                                </div>
                                <div className="text-truncate" style={{ maxWidth: '150px' }}>
                                  <small className="text-muted">{cat.image || cat.image_categorie}</small>
                                </div>
                              </div>
                            ) : (
                              <span className="text-muted fst-italic">Aucune image</span>
                            )}
                          </td>
                          <td className="text-end pe-3">
                            <Button
                              size="sm"
                              variant="outline-primary"
                              className="me-2 rounded-pill"
                              onClick={() => handleCatEdit(cat)}
                              title="Éditer la catégorie"
                            >
                              <FaPencilAlt className="me-1" /> Éditer
                            </Button>
                            <Button
                              size="sm"
                              variant="outline-danger"
                              className="rounded-pill"
                              onClick={() => handleCatDelete(cat.id)}
                              title="Supprimer la catégorie"
                            >
                              <FaTrashAlt className="me-1" /> Supprimer
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Custom CSS for table styling */}
          <style jsx="true">{`
            .color-dot {
              width: 10px;
              height: 10px;
              border-radius: 50%;
            }
            .image-thumbnail img {
              object-fit: cover;
            }
          `}</style>
        </>
      )}
      {tab === 'sous-categories' && (
        <>
          {sousCatError && <Alert variant="danger">{sousCatError}</Alert>}
          <Card className="mb-4 shadow-sm border-0">
            <Card.Header className="bg-white py-3">
              <h5 className="mb-0 fw-bold">{sousCatEditingId ? 'Modifier une sous-catégorie' : 'Ajouter une nouvelle sous-catégorie'}</h5>
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleSousCatSubmit}>
                <Row className="g-3">
                  <Col xs={12} md={4}>
                    <Form.Group controlId="sousCategorieNom">
                      <Form.Label className="fw-medium">Nom de la sous-catégorie</Form.Label>
                      <Form.Control
                        name="nom"
                        value={sousCatForm.nom || ''}
                        onChange={handleSousCatChange}
                        placeholder="Nom de la sous-catégorie"
                        required
                        disabled={sousCatSubmitting}
                        className="rounded-3 border-2"
                      />
                      <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="sousCategorieDescription">
                      <Form.Label className="fw-medium">Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="description"
                        value={sousCatForm.description || ''}
                        onChange={handleSousCatChange}
                        placeholder="Description détaillée de la sous-catégorie"
                        disabled={sousCatSubmitting}
                        className="rounded-3 border-2"
                      />
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="categorieId">
                      <Form.Label className="fw-medium">Catégorie parente</Form.Label>
                      <Form.Select
                        name="categorie_id"
                        value={sousCatForm.categorie_id || ''}
                        onChange={handleSousCatChange}
                        required
                        disabled={sousCatSubmitting || categories.length === 0}
                        className="rounded-3 border-2"
                      >
                        <option value="">Sélectionnez une catégorie</option>
                        {categories.map((cat) => (
                          <option key={cat.id} value={cat.id}>
                            {cat.nom || cat.nom_categorie}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">Sélectionnez la catégorie à laquelle cette sous-catégorie appartient.</Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
                <div className="d-flex gap-2 mt-4 justify-content-end">
                  {sousCatEditingId && (
                    <Button
                      variant="outline-secondary"
                      type="button"
                      onClick={() => {
                        setSousCatForm(initialSousCategoryForm);
                        setSousCatEditingId(null);
                      }}
                      className="px-4"
                    >
                      <i className="bi bi-x-circle me-2"></i>
                      Annuler
                    </Button>
                  )}
                  <Button type="submit" variant={sousCatEditingId ? 'warning' : 'primary'} disabled={sousCatSubmitting} className="px-4">
                    {sousCatSubmitting ? (
                      <>
                        <Spinner size="sm" animation="border" className="me-2" />
                        Traitement...
                      </>
                    ) : sousCatEditingId ? (
                      <>
                        <FaPencilAlt className="me-2" />
                        Mettre à jour
                      </>
                    ) : (
                      <>
                        <FaPlus className="me-2" />
                        Ajouter
                      </>
                    )}
                  </Button>
                </div>
              </Form>
              {/* Image management for selected sous-categorie */}
              {sousCatEditingId && (
                <div className="mt-4 pt-3 border-top">
                  <h6 className="mb-3 text-muted">Gestion des images</h6>
                  <ImageManager modelType="sous_categorie" modelId={sousCatEditingId} />
                </div>
              )}
            </Card.Body>
          </Card>
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des sous-catégories</h5>
              <div className="text-muted small">
                {sousCategories.length} sous-catégorie{sousCategories.length !== 1 ? 's' : ''} au total
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {sousCatLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="success" />
                  <p className="mt-3 text-muted">Chargement des sous-catégories...</p>
                </div>
              ) : sousCategories.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <i className="bi bi-folder text-muted" style={{ fontSize: '3rem' }}></i>
                  </div>
                  <p className="text-muted">Aucune sous-catégorie trouvée.</p>
                  <Button variant="success" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>
                    <FaPlus className="me-2" />
                    Créer une sous-catégorie
                  </Button>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table hover responsive className="align-middle mb-0">
                    <thead>
                      <tr className="bg-light">
                        <th className="ps-3" style={{ width: '60px' }}>
                          ID
                        </th>
                        <th style={{ width: '20%' }}>Nom</th>
                        <th style={{ width: '30%' }}>Description</th>
                        <th style={{ width: '25%' }}>Catégorie parente</th>
                        <th className="text-end pe-3" style={{ width: '25%' }}>
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {sousCategories.map((sc) => {
                        const parentCat = categories.find((cat) => cat.id === parseInt(sc.categorie_id));
                        return (
                          <tr key={sc.id} className="border-bottom">
                            <td className="ps-3 fw-medium">{sc.id}</td>
                            <td>
                              <div className="d-flex align-items-center">
                                <div className="color-dot bg-success me-2"></div>
                                <span className="fw-medium">{sc.nom || sc.nom_sous_categorie}</span>
                              </div>
                            </td>
                            <td>
                              <div className="text-truncate" style={{ maxWidth: '300px' }}>
                                {sc.description || sc.description_sous_categorie || (
                                  <span className="text-muted fst-italic">Aucune description</span>
                                )}
                              </div>
                            </td>
                            <td>
                              {parentCat ? (
                                <div className="d-flex align-items-center">
                                  <div className="color-dot bg-primary me-2"></div>
                                  <span>{parentCat.nom || parentCat.nom_categorie}</span>
                                </div>
                              ) : (
                                <span className="text-muted">{sc.categorie_id}</span>
                              )}
                            </td>
                            <td className="text-end pe-3">
                              <Button
                                size="sm"
                                variant="outline-primary"
                                className="me-2 rounded-pill"
                                onClick={() => handleSousCatEdit(sc)}
                                title="Éditer la sous-catégorie"
                              >
                                <FaPencilAlt className="me-1" /> Éditer
                              </Button>
                              <Button
                                size="sm"
                                variant="outline-danger"
                                className="rounded-pill"
                                onClick={() => handleSousCatDelete(sc.id)}
                                title="Supprimer la sous-catégorie"
                              >
                                <FaTrashAlt className="me-1" /> Supprimer
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>
        </>
      )}
      {tab === 'sous-sous-categories' && <SousSousCategories ImageManager={ImageManager} />}
      {tab === 'featured-categories' && <FeaturedCategories />}
    </Container>
  );
}
