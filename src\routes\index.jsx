import { createBrowserRouter } from 'react-router-dom';

// routes
import AuthenticationRoutes from './AuthenticationRoutes';
import MainRoutes from './MainRoutes';
import LandingPage from '../views/pages/LandingPage';

// ==============================|| ROUTING RENDER ||============================== //

const router = createBrowserRouter(
  [
    // Root route - landing page
    {
      path: '/',
      element: <LandingPage />
    },
    MainRoutes,
    AuthenticationRoutes,
    // Catch-all route for 404 errors
    {
      path: '*',
      element: (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100vh',
            textAlign: 'center',
            padding: '20px'
          }}
        >
          <h1>404 - Page Not Found</h1>
          <p>The page you're looking for doesn't exist.</p>
          <a href="/app/dashboard/default" style={{ color: '#1976d2', textDecoration: 'none' }}>
            Go to Dashboard
          </a>
        </div>
      )
    }
  ],
  {
    basename: import.meta.env.VITE_APP_BASE_NAME
  }
);

export default router;
