import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Button, Row, Col, <PERSON><PERSON>, Spin<PERSON>, <PERSON>ge, Modal, Table } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const AjoutCollection = () => {
  const API_URL = 'https://laravel-api.fly.dev/api';

  // Style cohérent
  const fontStyle = {
    fontFamily: "'Montserrat', sans-serif",
    fontWeight: 500
  };

  const colors = {
    primaryDark: '#2a3f5f',
    primaryLight: '#3a537b',
    accent: '#3a8dde',
    success: '#2ecc71',
    danger: '#e74c3c',
    warning: '#f39c12'
  };

  // États
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    image: '',
    active: true,
    date_debut: '',
    date_fin: '',
    produits: []
  });
  const [allProduits, setAllProduits] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showAddProduit, setShowAddProduit] = useState(false);
  const [newProduit, setNewProduit] = useState({
    id: '',
    ordre: '',
    featured: false
  });
  const [showCollectionsList, setShowCollectionsList] = useState(false);
  const [collections, setCollections] = useState([]);
  const [collectionsLoading, setCollectionsLoading] = useState(false);

  // Nombre total d'étapes
  const totalSteps = 3;

  // Chargement initial des données
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [produitsRes, collectionsRes] = await Promise.all([fetch(`${API_URL}/produits`), fetch(`${API_URL}/collections`)]);

        if (!produitsRes.ok) {
          throw new Error('Erreur de chargement des produits');
        }

        if (!collectionsRes.ok) {
          throw new Error('Erreur de chargement des collections');
        }

        const [produitsData, collectionsData] = await Promise.all([produitsRes.json(), collectionsRes.json()]);

        setAllProduits(produitsData.data || produitsData);
        setCollections(collectionsData.data || collectionsData);
        setLoading(false);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les données initiales');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Charger les collections
  const fetchCollections = async () => {
    try {
      setCollectionsLoading(true);
      const response = await fetch(`${API_URL}/collections`);
      if (!response.ok) throw new Error('Erreur de chargement');
      const data = await response.json();
      setCollections(data.data || data);
      setCollectionsLoading(false);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de charger la liste des collections');
      setCollectionsLoading(false);
    }
  };

  // Gestion des changements
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Gestion des changements pour les produits
  const handleProduitChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewProduit({
      ...newProduit,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Ajout d'un nouveau produit
  const addProduit = () => {
    if (!newProduit.id || !newProduit.ordre) {
      setError('Veuillez sélectionner un produit et spécifier un ordre');
      return;
    }

    const produitToAdd = allProduits.find((p) => p.id == newProduit.id);

    if (!produitToAdd) {
      setError('Produit sélectionné introuvable');
      return;
    }

    setFormData({
      ...formData,
      produits: [
        ...formData.produits,
        {
          ...newProduit,
          ...produitToAdd
        }
      ]
    });

    setNewProduit({
      id: '',
      ordre: '',
      featured: false
    });
    setShowAddProduit(false);
    setError(null);
  };

  // Suppression d'un produit
  const removeProduit = (index) => {
    const newProduits = [...formData.produits];
    newProduits.splice(index, 1);
    setFormData({
      ...formData,
      produits: newProduits
    });
  };

  // Navigation entre étapes
  const nextStep = () => {
    if (currentStep === 1 && !formData.nom) {
      setError('Veuillez remplir tous les champs obligatoires');
      return;
    }

    setError(null);
    setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  // Soumission du formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const produitsToSend = formData.produits.map((p) => ({
        id: p.id,
        ordre: p.ordre,
        featured: p.featured
      }));

      const response = await fetch(`${API_URL}/collections`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          produits: produitsToSend
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erreur lors de l'ajout");
      }

      const data = await response.json();

      setSuccess('Collection ajoutée avec succès!');
      setFormData({
        nom: '',
        description: '',
        image: '',
        active: true,
        date_debut: '',
        date_fin: '',
        produits: []
      });
      setCurrentStep(1);

      // Recharger les collections
      await fetchCollections();
    } catch (err) {
      console.error('Erreur:', err);
      setError(err.message || "Erreur lors de l'ajout de la collection");
    } finally {
      setLoading(false);
    }
  };

  // Barre de progression personnalisée
  const CustomProgressBar = () => {
    return (
      <div className="progress-container mb-4">
        <div className="d-flex align-items-center">
          {[...Array(totalSteps)].map((_, i) => (
            <React.Fragment key={i}>
              <div className={`step ${currentStep > i + 1 ? 'completed' : currentStep === i + 1 ? 'active' : ''}`}>
                <div className="step-circle">{currentStep > i + 1 ? <i className="fas fa-check"></i> : i + 1}</div>
                <span className="step-label">
                  {i === 0 && 'Informations'}
                  {i === 1 && 'Produits'}
                  {i === 2 && 'Confirmation'}
                </span>
              </div>
              {i < totalSteps - 1 && <div className={`step-connector ${currentStep > i + 1 ? 'completed' : ''}`}></div>}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Container className="py-4 collection-container" style={fontStyle}>
      {/* Titre principal */}
      <div className="text-center mb-5">
        <h1 className="mb-3 collection-title">AJOUTER UNE COLLECTION</h1>
        <div className="title-divider"></div>
      </div>

      {/* Messages d'alerte */}
      {error && (
        <Alert variant="danger" className="mb-4 alert-message" onClose={() => setError(null)} dismissible>
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4 alert-message" onClose={() => setSuccess(null)} dismissible>
          <i className="fas fa-check-circle me-2"></i>
          {success}
        </Alert>
      )}

      {/* Carte principale */}
      <Card className="shadow-sm main-card">
        <Card.Body>
          <Form onSubmit={handleSubmit}>
            <CustomProgressBar />

            {/* Étape 1: Informations de base */}
            {currentStep === 1 && (
              <div className="form-section">
                <h5 className="mb-4 section-title">Informations de la collection</h5>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3 form-group">
                      <Form.Label>
                        Nom de la collection <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="nom"
                        value={formData.nom}
                        onChange={handleChange}
                        placeholder="Nom de la collection"
                        required
                        className="form-control"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group className="mb-3 form-group">
                      <Form.Label>Image</Form.Label>
                      <Form.Control
                        type="text"
                        name="image"
                        value={formData.image}
                        onChange={handleChange}
                        placeholder="URL de l'image"
                        className="form-control"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={12}>
                    <Form.Group className="mb-3 form-group">
                      <Form.Label>Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        rows={3}
                        placeholder="Description de la collection"
                        className="form-control"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3 form-group">
                      <Form.Check
                        type="switch"
                        name="active"
                        label="Collection active"
                        checked={formData.active}
                        onChange={handleChange}
                        className="form-check"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3 form-group">
                      <Form.Label>Date de début</Form.Label>
                      <Form.Control
                        type="date"
                        name="date_debut"
                        value={formData.date_debut}
                        onChange={handleChange}
                        className="form-control"
                      />
                    </Form.Group>
                  </Col>

                  <Col md={4}>
                    <Form.Group className="mb-3 form-group">
                      <Form.Label>Date de fin</Form.Label>
                      <Form.Control
                        type="date"
                        name="date_fin"
                        value={formData.date_fin}
                        onChange={handleChange}
                        className="form-control"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </div>
            )}

            {/* Étape 2: Produits */}
            {currentStep === 2 && (
              <div className="form-section">
                <h5 className="mb-4 section-title">Produits de la collection</h5>

                {loading && (
                  <div className="text-center my-4 loading-spinner">
                    <Spinner animation="border" variant="primary" />
                    <p>Chargement des produits...</p>
                  </div>
                )}

                <div className="mb-4">
                  <Button
                    variant="outline-primary"
                    onClick={() => setShowAddProduit(true)}
                    disabled={loading || allProduits.length === 0}
                    className="add-product-btn"
                  >
                    <i className="fas fa-plus me-2"></i>
                    Ajouter un produit
                  </Button>

                  {formData.produits.length > 0 ? (
                    <div className="mt-4 products-table-container">
                      <h6>Produits ajoutés:</h6>
                      <div className="table-responsive">
                        <Table striped bordered hover className="products-table">
                          <thead>
                            <tr>
                              <th>Produit</th>
                              <th>Prix</th>
                              <th>Ordre</th>
                              <th>Mis en avant</th>
                              <th>Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {formData.produits
                              .sort((a, b) => a.ordre - b.ordre)
                              .map((produit, index) => (
                                <tr key={index}>
                                  <td>{produit.nom_produit}</td>
                                  <td>{produit.prix_produit} €</td>
                                  <td>{produit.ordre}</td>
                                  <td>
                                    {produit.featured ? (
                                      <i className="fas fa-check text-success"></i>
                                    ) : (
                                      <i className="fas fa-times text-danger"></i>
                                    )}
                                  </td>
                                  <td>
                                    <Button variant="outline-danger" size="sm" onClick={() => removeProduit(index)} className="action-btn">
                                      <i className="fas fa-trash"></i>
                                    </Button>
                                  </td>
                                </tr>
                              ))}
                          </tbody>
                        </Table>
                      </div>
                    </div>
                  ) : (
                    <Alert variant="info" className="mt-3 no-products-alert">
                      Aucun produit ajouté à cette collection
                    </Alert>
                  )}
                </div>
              </div>
            )}

            {/* Étape 3: Confirmation */}
            {currentStep === 3 && (
              <div className="form-section">
                <h5 className="mb-4 section-title">Confirmation</h5>
                <Card className="mb-4 confirmation-card">
                  <Card.Body>
                    <h6 className="mb-3">Récapitulatif des informations</h6>

                    <Row>
                      <Col md={6}>
                        <div className="mb-3">
                          <strong>Nom:</strong> {formData.nom}
                        </div>
                        <div className="mb-3">
                          <strong>Description:</strong> {formData.description || 'Non renseignée'}
                        </div>
                        <div className="mb-3">
                          <strong>Image:</strong> {formData.image || 'Non renseignée'}
                        </div>
                      </Col>

                      <Col md={6}>
                        <div className="mb-3">
                          <strong>Statut:</strong>
                          <Badge bg={formData.active ? 'success' : 'secondary'} className="ms-2">
                            {formData.active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <div className="mb-3">
                          <strong>Dates:</strong>
                          {formData.date_debut || formData.date_fin ? (
                            <span className="ms-2">
                              {formData.date_debut ? `Du ${formData.date_debut}` : ''}
                              {formData.date_fin ? ` au ${formData.date_fin}` : ''}
                            </span>
                          ) : (
                            'Permanente'
                          )}
                        </div>
                      </Col>
                    </Row>

                    {formData.produits.length > 0 ? (
                      <div className="mt-3 products-summary">
                        <strong>Produits ({formData.produits.length}):</strong>
                        <ul className="mt-2">
                          {formData.produits
                            .sort((a, b) => a.ordre - b.ordre)
                            .map((produit, index) => (
                              <li key={index}>
                                {produit.nom_produit} (Ordre: {produit.ordre}) -{produit.featured && ' ★ Mis en avant'}
                              </li>
                            ))}
                        </ul>
                      </div>
                    ) : (
                      <Alert variant="warning" className="mt-3 no-products-alert">
                        Aucun produit dans cette collection
                      </Alert>
                    )}
                  </Card.Body>
                </Card>

                <Form.Group className="mb-3 confirmation-check">
                  <Form.Check type="checkbox" label="Je confirme que les informations ci-dessus sont exactes" required />
                </Form.Group>
              </div>
            )}

            {/* Boutons de navigation */}
            <div className="d-flex justify-content-between mt-4 pt-3 border-top navigation-buttons">
              {currentStep > 1 ? (
                <Button variant="outline-primary" onClick={prevStep} disabled={loading} className="prev-btn">
                  <i className="fas fa-arrow-left me-2"></i>
                  Précédent
                </Button>
              ) : (
                <div></div>
              )}

              {currentStep < totalSteps ? (
                <Button variant="primary" onClick={nextStep} disabled={loading} className="next-btn">
                  Suivant
                  <i className="fas fa-arrow-right ms-2"></i>
                </Button>
              ) : (
                <Button variant="success" type="submit" disabled={loading} className="submit-btn">
                  {loading ? (
                    <>
                      <Spinner as="span" size="sm" animation="border" className="me-2" />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-check me-2"></i>
                      Confirmer et ajouter
                    </>
                  )}
                </Button>
              )}
            </div>
          </Form>
        </Card.Body>
      </Card>

      {/* Bouton pour voir la liste des collections */}
      <div className="text-center mt-4">
        <Button
          variant="outline-primary"
          onClick={() => {
            fetchCollections();
            setShowCollectionsList(true);
          }}
          className="view-collections-btn"
        >
          <i className="fas fa-list me-2"></i>
          Voir la liste des collections
        </Button>
      </div>

      {/* Modal d'ajout de produit */}
      <Modal show={showAddProduit} onHide={() => setShowAddProduit(false)} className="add-product-modal">
        <Modal.Header closeButton>
          <Modal.Title>Ajouter un produit</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>
                Produit <span className="text-danger">*</span>
              </Form.Label>
              {allProduits.length > 0 ? (
                <Form.Select name="id" value={newProduit.id} onChange={handleProduitChange} required className="form-control">
                  <option value="">Sélectionner un produit</option>
                  {allProduits.map((produit) => (
                    <option key={produit.id} value={produit.id}>
                      {produit.nom_produit} - {produit.prix_produit}€
                    </option>
                  ))}
                </Form.Select>
              ) : (
                <Alert variant="warning">Aucun produit disponible</Alert>
              )}
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>
                Ordre d'affichage <span className="text-danger">*</span>
              </Form.Label>
              <Form.Control
                type="number"
                name="ordre"
                value={newProduit.ordre}
                onChange={handleProduitChange}
                placeholder="Position dans la collection"
                min="1"
                required
                className="form-control"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="featured"
                label="Mettre en avant ce produit"
                checked={newProduit.featured}
                onChange={handleProduitChange}
                className="form-check"
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddProduit(false)} className="cancel-btn">
            Annuler
          </Button>
          <Button variant="primary" onClick={addProduit} disabled={!newProduit.id || !newProduit.ordre} className="add-btn">
            <i className="fas fa-plus me-2"></i>
            Ajouter le produit
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal pour la liste des collections */}
      <Modal
        show={showCollectionsList}
        onHide={() => setShowCollectionsList(false)}
        size="xl"
        scrollable
        className="collections-list-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title>Liste des collections</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {collectionsLoading ? (
            <div className="text-center my-4 loading-spinner">
              <Spinner animation="border" variant="primary" />
              <p>Chargement des collections...</p>
            </div>
          ) : collections.length === 0 ? (
            <Alert variant="info">Aucune collection disponible</Alert>
          ) : (
            <div className="table-responsive">
              <Table striped bordered hover className="collections-table">
                <thead>
                  <tr>
                    <th>Nom</th>
                    <th>Description</th>
                    <th>Statut</th>
                    <th>Produits</th>
                    <th>Dates</th>
                  </tr>
                </thead>
                <tbody>
                  {collections.map((collection) => (
                    <tr key={collection.id}>
                      <td>{collection.nom}</td>
                      <td>{collection.description || '-'}</td>
                      <td>
                        <Badge bg={collection.active ? 'success' : 'secondary'}>{collection.active ? 'Active' : 'Inactive'}</Badge>
                      </td>
                      <td>{collection.produits?.length || 0}</td>
                      <td>
                        {collection.date_debut ? new Date(collection.date_debut).toLocaleDateString() : '-'}
                        {' → '}
                        {collection.date_fin ? new Date(collection.date_fin).toLocaleDateString() : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCollectionsList(false)} className="close-btn">
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Styles CSS */}
      <style jsx>{`
        .collection-container {
          padding: 2rem 1rem;
        }

        .collection-title {
          font-size: 2.2rem;
          font-weight: 600;
          color: #2a3f5f;
          letter-spacing: 1px;
        }

        .title-divider {
          height: 3px;
          width: 120px;
          background: linear-gradient(90deg, rgba(58, 83, 155, 0.2) 0%, rgba(58, 83, 155, 1) 50%, rgba(58, 83, 155, 0.2) 100%);
          border-radius: 3px;
          margin: 0 auto;
        }

        .alert-message {
          border-radius: 0.5rem;
        }

        .main-card {
          border-radius: 0.5rem;
          border: none;
        }

        .progress-container {
          margin-bottom: 2rem;
        }

        .step {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          z-index: 1;
          padding: 0 1rem;
        }

        .step-circle {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 0.5rem;
          background-color: #e9ecef;
          color: #6c757d;
        }

        .step.active .step-circle {
          background-color: #3a8dde;
          color: white;
          font-weight: bold;
        }

        .step.completed .step-circle {
          background-color: #3a8dde;
          color: white;
        }

        .step-label {
          font-size: 0.9rem;
        }

        .step-connector {
          flex-grow: 1;
          height: 2px;
          background-color: #e9ecef;
          margin: 0 -1rem;
          position: relative;
          top: 20px;
          z-index: 0;
        }

        .step-connector.completed {
          background-color: #3a8dde;
        }

        .form-section {
          margin-bottom: 2rem;
        }

        .section-title {
          color: #3a8dde;
        }

        .form-group {
          margin-bottom: 1.5rem;
        }

        .form-control,
        .form-select {
          border-radius: 0.5rem;
        }

        .add-product-btn {
          border-radius: 0.5rem;
        }

        .products-table-container {
          margin-top: 1.5rem;
        }

        .products-table {
          border-radius: 0.5rem;
          overflow: hidden;
        }

        .no-products-alert {
          border-radius: 0.5rem;
        }

        .confirmation-card {
          border-radius: 0.5rem;
          border: none;
        }

        .products-summary ul {
          padding-left: 1.5rem;
        }

        .navigation-buttons {
          border-top: 1px solid #dee2e6;
        }

        .prev-btn,
        .next-btn,
        .submit-btn {
          border-radius: 0.5rem;
          min-width: 120px;
        }

        .view-collections-btn {
          border-radius: 0.5rem;
        }

        .add-product-modal .modal-content {
          border-radius: 0.5rem;
        }

        .cancel-btn,
        .add-btn {
          border-radius: 0.5rem;
        }

        .collections-list-modal .modal-content {
          border-radius: 0.5rem;
        }

        .collections-table {
          border-radius: 0.5rem;
          overflow: hidden;
        }

        .close-btn {
          border-radius: 0.5rem;
        }

        @media (max-width: 768px) {
          .collection-container {
            padding: 1rem;
          }

          .collection-title {
            font-size: 1.8rem;
          }

          .form-group {
            margin-bottom: 1rem;
          }

          .step {
            padding: 0 0.5rem;
          }

          .step-circle {
            width: 30px;
            height: 30px;
            font-size: 0.9rem;
          }

          .step-label {
            font-size: 0.8rem;
          }

          .step-connector {
            margin: 0 -0.5rem;
          }

          .prev-btn,
          .next-btn,
          .submit-btn {
            min-width: 100px;
            padding: 0.375rem 0.75rem;
            font-size: 0.9rem;
          }
        }

        @media (max-width: 576px) {
          .progress-container .d-flex {
            flex-direction: column;
            align-items: flex-start;
          }

          .step {
            flex-direction: row;
            margin-bottom: 1rem;
            width: 100%;
          }

          .step-circle {
            margin-bottom: 0;
            margin-right: 1rem;
          }

          .step-connector {
            display: none;
          }

          .navigation-buttons {
            flex-direction: column;
          }

          .prev-btn,
          .next-btn,
          .submit-btn {
            width: 100%;
            margin-bottom: 0.5rem;
          }

          .products-table th,
          .products-table td {
            padding: 0.5rem;
            font-size: 0.9rem;
          }
        }
      `}</style>
    </Container>
  );
};

export default AjoutCollection;
