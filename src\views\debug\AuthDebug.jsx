import React, { useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, But<PERSON>, Alert, Form } from 'react-bootstrap';

const AuthDebug = () => {
  const [debugInfo, setDebugInfo] = useState('');
  const [devModeEnabled, setDevModeEnabled] = useState(
    localStorage.getItem('auth_dev_mode') === 'true'
  );

  const checkAuthState = () => {
    const authData = {
      accessToken: localStorage.getItem('access_token'),
      refreshToken: localStorage.getItem('refresh_token'),
      idToken: localStorage.getItem('id_token'),
      userData: localStorage.getItem('user_data'),
      userLoggedOut: sessionStorage.getItem('user_logged_out'),
      authRedirectUrl: sessionStorage.getItem('auth_redirect_url'),
      processedAuthCode: sessionStorage.getItem('processed_auth_code'),
      apiUrl: import.meta.env.VITE_REACT_APP_API_URL || 'Not set',
      keycloakUrl: import.meta.env.REACT_APP_KEYCLOAK_URL || 'Not set',
      keycloakRealm: import.meta.env.REACT_APP_KEYCLOAK_REALM || 'Not set',
      keycloakClientId: import.meta.env.REACT_APP_KEYCLOAK_CLIENT_ID || 'Not set'
    };

    setDebugInfo(JSON.stringify(authData, null, 2));
  };

  const clearAuthData = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('id_token');
    localStorage.removeItem('user_data');
    sessionStorage.removeItem('user_logged_out');
    sessionStorage.removeItem('auth_redirect_url');
    sessionStorage.removeItem('processed_auth_code');
    alert('Authentication data cleared');
    checkAuthState();
  };

  const toggleDevMode = () => {
    const newValue = !devModeEnabled;
    setDevModeEnabled(newValue);
    if (newValue) {
      localStorage.setItem('auth_dev_mode', 'true');
    } else {
      localStorage.removeItem('auth_dev_mode');
    }
  };

  const testApiConnection = async () => {
    const apiUrl = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';
    
    try {
      // Test basic API connectivity
      const response = await fetch(`${apiUrl}/commandes`);
      alert(`API Test: ${response.status} ${response.statusText}`);
    } catch (error) {
      alert(`API Test Failed: ${error.message}`);
    }
  };

  const testAuthEndpoint = async () => {
    const apiUrl = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';
    
    try {
      const response = await fetch(`${apiUrl}/auth/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access_token: 'test-token'
        })
      });
      
      const responseText = await response.text();
      alert(`Auth Endpoint Test: ${response.status} ${response.statusText}\n${responseText}`);
    } catch (error) {
      alert(`Auth Endpoint Test Failed: ${error.message}`);
    }
  };

  return (
    <Container className="py-4">
      <Card>
        <Card.Header>
          <h3>Authentication Debug Panel</h3>
        </Card.Header>
        <Card.Body>
          <div className="mb-3">
            <Button variant="primary" onClick={checkAuthState} className="me-2">
              Check Auth State
            </Button>
            <Button variant="warning" onClick={clearAuthData} className="me-2">
              Clear Auth Data
            </Button>
            <Button variant="info" onClick={testApiConnection} className="me-2">
              Test API Connection
            </Button>
            <Button variant="secondary" onClick={testAuthEndpoint}>
              Test Auth Endpoint
            </Button>
          </div>

          <Form.Check
            type="switch"
            id="dev-mode-switch"
            label="Enable Development Mode (Bypass Authentication)"
            checked={devModeEnabled}
            onChange={toggleDevMode}
            className="mb-3"
          />

          {devModeEnabled && (
            <Alert variant="warning">
              <strong>Development Mode Enabled!</strong> Authentication will be bypassed.
              This should only be used for development and testing.
            </Alert>
          )}

          {debugInfo && (
            <div>
              <h5>Authentication State:</h5>
              <pre style={{ background: '#f8f9fa', padding: '1rem', borderRadius: '4px' }}>
                {debugInfo}
              </pre>
            </div>
          )}

          <div className="mt-3">
            <h5>Instructions:</h5>
            <ol>
              <li>Click "Check Auth State" to see current authentication data</li>
              <li>Click "Clear Auth Data" to reset authentication</li>
              <li>Click "Test API Connection" to verify backend connectivity</li>
              <li>Click "Test Auth Endpoint" to test authentication endpoint</li>
              <li>Enable "Development Mode" to bypass authentication for testing</li>
            </ol>
          </div>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default AuthDebug;
