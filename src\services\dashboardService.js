import axios from 'axios';

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Create axios instance with auth headers
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 10000,
});

// Add auth headers to requests
axiosInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Helper function to get status label
const getStatusLabel = (status) => {
  const statusMap = {
    en_attente: 'En attente',
    confirmee: 'Confirmée',
    en_preparation: 'En préparation',
    expediee: 'Expédiée',
    livree: 'Livrée',
    annulee: 'Annulée',
    remboursee: '<PERSON><PERSON><PERSON><PERSON>',
    retournee: 'Retournée'
  };

  return statusMap[status] || status || 'En attente';
};

// Dashboard metrics service
export async function fetchDashboardMetrics() {
  try {
    console.log('🔄 Fetching dashboard data from API:', API_URL);
    console.log('🔑 Using token:', localStorage.getItem('access_token') ? 'Token found' : 'No token');

    // Fetch all required data in parallel with proper error handling
    const [productsRes, ordersRes, clientsRes] = await Promise.allSettled([
      axiosInstance.get('/produits'),
      axiosInstance.get('/commandes', { params: { with: 'user,client' } }),
      axiosInstance.get('/clients')
    ]);

    console.log('📊 API responses:', {
      products: {
        status: productsRes.status,
        fulfilled: productsRes.status === 'fulfilled',
        error: productsRes.status === 'rejected' ? productsRes.reason?.message : null
      },
      orders: {
        status: ordersRes.status,
        fulfilled: ordersRes.status === 'fulfilled',
        error: ordersRes.status === 'rejected' ? ordersRes.reason?.message : null
      },
      clients: {
        status: clientsRes.status,
        fulfilled: clientsRes.status === 'fulfilled',
        error: clientsRes.status === 'rejected' ? clientsRes.reason?.message : null
      }
    });

    // Handle individual responses gracefully
    let productsData = { data: [] };
    let ordersData = { data: [] };
    let clientsData = { data: [] };

    if (productsRes.status === 'fulfilled') {
      const response = productsRes.value.data;
      console.log('✅ Products full response:', response);

      // Products API returns: {"success": true, "data": [...]}
      if (response && response.success && response.data) {
        productsData = { data: response.data };
        console.log('✅ Products data loaded:', response.data.length, 'products');
      } else {
        console.warn('⚠️ Products response format unexpected:', response);
      }
    } else {
      console.error('❌ Products fetch failed:', productsRes.reason);
    }

    if (ordersRes.status === 'fulfilled') {
      const response = ordersRes.value.data;
      console.log('✅ Orders full response:', response);

      // Orders API returns: {"success": true, "data": {"current_page": 1, "data": [...]}}
      if (response && response.success && response.data && response.data.data) {
        ordersData = { data: response.data.data };
        console.log('✅ Orders data loaded:', response.data.data.length, 'orders');
      } else {
        console.warn('⚠️ Orders response format unexpected:', response);
      }
    } else {
      console.error('❌ Orders fetch failed:', ordersRes.reason);
    }

    if (clientsRes.status === 'fulfilled') {
      const response = clientsRes.value.data;
      console.log('✅ Clients full response:', response);

      // Clients API returns: [{...}, {...}] (direct array)
      if (Array.isArray(response)) {
        clientsData = { data: response };
        console.log('✅ Clients data loaded:', response.length, 'clients');
      } else if (response && response.success && response.data) {
        // Fallback for wrapped format
        clientsData = { data: response.data };
        console.log('✅ Clients data loaded (wrapped):', response.data.length, 'clients');
      } else {
        console.warn('⚠️ Clients response format unexpected:', response);
      }
    } else {
      console.error('❌ Clients fetch failed:', clientsRes.reason);
    }

    // Extract totals
    const totalProducts = Array.isArray(productsData.data) ? productsData.data.length :
                         Array.isArray(productsData) ? productsData.length : 0;

    const totalOrders = Array.isArray(ordersData.data) ? ordersData.data.length :
                       Array.isArray(ordersData) ? ordersData.length : 0;

    const totalClients = Array.isArray(clientsData.data) ? clientsData.data.length :
                        Array.isArray(clientsData) ? clientsData.length : 0;

    // Get recent orders (last 5) with improved data extraction
    const ordersArray = ordersData.data || ordersData || [];
    const recentOrders = Array.isArray(ordersArray) ?
      ordersArray
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5)
        .map(order => ({
          id: order.id,
          order_number: order.numero_commande || `CMD-${String(order.id).padStart(6, '0')}`,
          customer_name: order.user?.name || order.client?.nom || order.client?.name || order.nom_client || 'Client inconnu',
          total: parseFloat(order.total_commande || order.total || 0),
          status: getStatusLabel(order.status || order.statut_commande || 'en_attente'),
          created_at: order.created_at
        })) : [];

    console.log('📋 Recent orders processed:', recentOrders.length);

    // Calculate total revenue from all orders
    const totalRevenue = Array.isArray(ordersArray) ?
      ordersArray.reduce((sum, order) => {
        return sum + parseFloat(order.total_commande || order.total || 0);
      }, 0) : 0;

    console.log('💰 Total revenue calculated:', totalRevenue);

    // Calculate sales data for the chart (last 6 months)
    const salesData = calculateSalesData(ordersArray);

    return {
      totalProducts,
      totalOrders,
      totalClients,
      totalRevenue,
      recentOrders,
      salesData
    };
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    throw error;
  }
}

// Helper function to calculate sales data for charts
function calculateSalesData(orders) {
  if (!Array.isArray(orders)) {
    console.log('⚠️ Orders is not an array:', typeof orders);
    return [];
  }

  const now = new Date();

  // Initialize months array for last 6 months
  const months = [];
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    months.push({
      month: date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' }),
      sales: 0,
      orderCount: 0,
      monthKey: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    });
  }

  console.log('📅 Initialized months:', months.map(m => m.month));

  // Aggregate sales by month
  orders.forEach(order => {
    if (!order.created_at) return;

    const orderDate = new Date(order.created_at);
    const orderMonthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;

    const monthIndex = months.findIndex(m => m.monthKey === orderMonthKey);

    if (monthIndex !== -1) {
      const orderTotal = parseFloat(order.total_commande || order.total || 0);
      months[monthIndex].sales += orderTotal;
      months[monthIndex].orderCount += 1;

      console.log(`📊 Added order ${order.id}: ${orderTotal} DT to ${months[monthIndex].month}`);
    }
  });

  console.log('📈 Final sales data:', months);
  return months;
}

// Fetch specific metrics
export async function fetchProductsCount() {
  try {
    const response = await axiosInstance.get('/produits');
    const data = response.data;
    // Products API returns: {"success": true, "data": [...]}
    if (data && data.success && Array.isArray(data.data)) {
      return data.data.length;
    }
    return 0;
  } catch (error) {
    console.error('Error fetching products count:', error);
    return 0;
  }
}

export async function fetchOrdersCount() {
  try {
    const response = await axiosInstance.get('/commandes');
    const data = response.data;
    // Orders API returns: {"success": true, "data": {"current_page": 1, "data": [...]}}
    if (data && data.success && data.data && Array.isArray(data.data.data)) {
      return data.data.data.length;
    }
    return 0;
  } catch (error) {
    console.error('Error fetching orders count:', error);
    return 0;
  }
}

export async function fetchClientsCount() {
  try {
    const response = await axiosInstance.get('/clients');
    const data = response.data;
    // Clients API returns: [{...}, {...}] (direct array)
    if (Array.isArray(data)) {
      return data.length;
    }
    return 0;
  } catch (error) {
    console.error('Error fetching clients count:', error);
    return 0;
  }
}

export async function fetchRecentOrders(limit = 5) {
  try {
    const response = await axiosInstance.get('/commandes', {
      params: { with: 'user,client' }
    });
    const data = response.data;

    // Orders API returns: {"success": true, "data": {"current_page": 1, "data": [...]}}
    let ordersArray = [];
    if (data && data.success && data.data && Array.isArray(data.data.data)) {
      ordersArray = data.data.data;
    }

    return Array.isArray(ordersArray) ?
      ordersArray
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, limit)
        .map(order => ({
          id: order.id,
          order_number: order.numero_commande || `CMD-${String(order.id).padStart(6, '0')}`,
          customer_name: order.user?.name || order.client?.nom || order.client?.name || order.nom_client || 'Client inconnu',
          total: parseFloat(order.total_commande || order.total || 0),
          status: getStatusLabel(order.status || order.statut_commande || 'en_attente'),
          created_at: order.created_at
        })) : [];
  } catch (error) {
    console.error('Error fetching recent orders:', error);
    return [];
  }
}
