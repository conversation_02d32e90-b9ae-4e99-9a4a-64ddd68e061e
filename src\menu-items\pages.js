// assets
import { <PERSON>con<PERSON>ey } from '@tabler/icons-react';

// constant
const icons = {
  IconKey
};

// ==============================|| EXTRA PAGES MENU ITEMS ||============================== //

// Authentication pages are handled automatically by Keycloak
// No manual navigation to login/register pages needed
const pages = {
  id: 'pages',
  title: 'Pages',
  caption: 'System Pages',
  icon: icons.IconKey,
  type: 'group',
  children: [
    // Authentication is handled automatically by Keycloak
    // Users will be redirected to Keycloak when needed
  ]
};

export default pages;
