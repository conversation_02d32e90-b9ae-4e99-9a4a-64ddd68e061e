import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// third party
import Chart from 'react-apexcharts';

// project imports
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import MainCard from 'ui-component/cards/MainCard';
import { gridSpacing } from 'store/constant';

const status = [
  {
    value: 'sales',
    label: 'Ventes'
  },
  {
    value: 'orders',
    label: 'Commandes'
  }
];

export default function SimpleSalesChart({ isLoading = false, data = [], error = null }) {
  const theme = useTheme();
  const [value, setValue] = useState('sales');

  // Prepare chart data safely
  const prepareChartData = () => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      // Return empty data structure
      return {
        categories: [],
        salesData: [],
        ordersData: [],
        isEmpty: true
      };
    }

    try {
      const categories = data.map((item, index) => {
        if (item && typeof item === 'object' && item.month) {
          return String(item.month);
        }
        return `Mois ${index + 1}`;
      });

      const salesData = data.map((item) => {
        if (item && typeof item === 'object') {
          const sales = Number(item.sales);
          return isNaN(sales) ? 0 : sales;
        }
        return 0;
      });

      const ordersData = data.map((item) => {
        if (item && typeof item === 'object') {
          const orders = Number(item.orderCount);
          return isNaN(orders) ? 0 : orders;
        }
        return 0;
      });

      return {
        categories,
        salesData,
        ordersData,
        isEmpty: false
      };
    } catch (err) {
      console.error('Error preparing chart data:', err);
      return {
        categories: [],
        salesData: [],
        ordersData: [],
        isEmpty: true
      };
    }
  };

  const chartData = prepareChartData();

  // Chart configuration
  const getChartOptions = () => {
    const currentData = value === 'sales' ? chartData.salesData : chartData.ordersData;
    
    return {
      chart: {
        type: 'bar',
        height: 350,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          borderRadius: 4
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: chartData.categories,
        labels: {
          style: {
            colors: theme.palette.text.secondary
          }
        }
      },
      yaxis: {
        title: {
          text: value === 'sales' ? 'Montant (DT)' : 'Nombre de commandes',
          style: {
            color: theme.palette.text.secondary
          }
        },
        labels: {
          style: {
            colors: theme.palette.text.secondary
          },
          formatter: function (val) {
            if (value === 'sales') {
              return Math.round(val) + ' DT';
            }
            return Math.round(val);
          }
        }
      },
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: function (val) {
            if (value === 'sales') {
              return val.toFixed(2) + ' DT';
            }
            return val + ' commandes';
          }
        }
      },
      colors: [value === 'sales' ? theme.palette.primary.main : theme.palette.secondary.main],
      grid: {
        borderColor: theme.palette.divider
      }
    };
  };

  const getChartSeries = () => {
    const currentData = value === 'sales' ? chartData.salesData : chartData.ordersData;
    
    return [{
      name: value === 'sales' ? 'Ventes (DT)' : 'Nombre de Commandes',
      data: currentData
    }];
  };

  // Calculate total for display
  const calculateTotal = () => {
    if (chartData.isEmpty) return 0;
    
    const currentData = value === 'sales' ? chartData.salesData : chartData.ordersData;
    return currentData.reduce((sum, val) => sum + val, 0);
  };

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: 'error.lighter',
                borderRadius: 2,
                border: 2,
                borderColor: 'error.main'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données de ventes: {error}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard>
      <Grid container spacing={gridSpacing}>
        <Grid size={12}>
          <Grid container sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
            <Grid>
              <Grid container direction="column" spacing={1}>
                <Grid>
                  <Typography variant="subtitle2">Évolution des Ventes</Typography>
                </Grid>
                <Grid>
                  <Typography variant="h3">
                    {value === 'sales' 
                      ? `${calculateTotal().toFixed(0)} DT`
                      : `${calculateTotal()} commandes`
                    }
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <Grid>
              <TextField 
                id="chart-select-type" 
                select 
                value={value} 
                onChange={(e) => setValue(e.target.value)}
                size="small"
              >
                {status.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
          </Grid>
        </Grid>
        <Grid size={12}>
          {chartData.isEmpty ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '350px',
                backgroundColor: 'grey.50',
                borderRadius: 2,
                border: 2,
                borderColor: 'grey.300',
                borderStyle: 'dashed'
              }}
            >
              <Typography variant="h6" sx={{ color: 'grey.600', mb: 1 }}>
                📊 Aucune donnée disponible
              </Typography>
              <Typography variant="body2" sx={{ color: 'grey.500', textAlign: 'center' }}>
                Les données de ventes seront affichées ici une fois disponibles.
              </Typography>
            </Box>
          ) : (
            <Chart
              options={getChartOptions()}
              series={getChartSeries()}
              type="bar"
              height={350}
            />
          )}
        </Grid>
      </Grid>
    </MainCard>
  );
}

SimpleSalesChart.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.array,
  error: PropTypes.string
};
