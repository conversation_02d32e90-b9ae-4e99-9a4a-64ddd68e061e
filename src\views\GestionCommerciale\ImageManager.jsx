import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, Spinner, Form, Image, Row, Col, Alert, Modal } from 'react-bootstrap';
import axios from 'axios';

const API_URL = 'https://laravel-api.fly.dev/api';

export default function ImageManager({ modelType, modelId, disabled }) {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [editImage, setEditImage] = useState(null);
  const [editForm, setEditForm] = useState({ alt_text: '', title: '' });
  const fileInputRef = useRef();

  const fetchImages = async () => {
    if (!modelId) return;
    setLoading(true);
    setError('');
    try {
      console.log(`Fetching images for ${modelType} with ID ${modelId}`);
      const res = await axios.get(`https://laravel-api.fly.dev/api/images/get`, {
        params: { model_type: modelType, model_id: modelId },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      });
      console.log('Image fetch response:', res.data);
      setImages(res.data.images || res.data || []);
    } catch (e) {
      console.error('Error fetching images:', e);
      setError(`Erreur lors du chargement des images: ${e.message}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchImages();
    // eslint-disable-next-line
  }, [modelId]);

  const handleUpload = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('image', file);
    formData.append('model_type', modelType);
    formData.append('model_id', modelId);
    setUploading(true);
    setError('');
    setSuccess('');
    try {
      console.log(`Uploading image for ${modelType} with ID ${modelId}`);
      const res = await axios.post(`https://laravel-api.fly.dev/api/images/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Accept: 'application/json'
        }
      });
      console.log('Upload response:', res.data);
      setSuccess('Image téléchargée avec succès');
      fetchImages();
    } catch (e) {
      console.error('Error uploading image:', e);
      setError(`Erreur lors de l'upload: ${e.message}`);
    }
    setUploading(false);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Supprimer cette image ?')) return;
    setError('');
    try {
      await axios.delete(`https://laravel-api.fly.dev/api/images/${id}`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      });
      fetchImages();
    } catch (e) {
      console.error('Error deleting image:', e);
      setError(`Erreur lors de la suppression: ${e.message}`);
    }
  };

  const handleSetPrimary = async (id) => {
    setError('');
    try {
      await axios.put(
        `https://laravel-api.fly.dev/api/images/${id}`,
        { is_primary: true },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json'
          }
        }
      );
      fetchImages();
    } catch (e) {
      console.error('Error setting primary image:', e);
      setError(`Erreur lors de la mise à jour: ${e.message}`);
    }
  };

  const openEditModal = (img) => {
    setEditImage(img);
    setEditForm({ alt_text: img.alt_text || '', title: img.title || '' });
    setShowEditModal(true);
  };

  const handleEditChange = (e) => {
    setEditForm({ ...editForm, [e.target.name]: e.target.value });
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    if (!editImage) return;
    setError('');
    try {
      await axios.put(`https://laravel-api.fly.dev/api/images/${editImage.id}`, editForm, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      });
      setShowEditModal(false);
      fetchImages();
    } catch (e) {
      console.error('Error updating image:', e);
      setError(`Erreur lors de la modification: ${e.message}`);
    }
  };

  return (
    <div className="mb-3">
      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}
      <Form.Group controlId="imageUpload" className="mb-2">
        <Form.Label>Ajouter une image</Form.Label>
        <Form.Control
          type="file"
          accept="image/*"
          onChange={handleUpload}
          ref={fileInputRef}
          disabled={uploading || disabled || !modelId}
        />
      </Form.Group>
      {loading ? (
        <Spinner animation="border" />
      ) : (
        <Row>
          {images.map((img) => (
            <Col key={img.id} xs={6} md={4} lg={3} className="mb-3">
              <div className="border p-2 position-relative">
                <Image
                  src={img.thumbnail_medium || img.direct_url}
                  alt={img.alt_text}
                  title={img.title}
                  fluid
                  rounded
                  style={{ width: '100%', height: 120, objectFit: 'cover' }}
                />
                {img.is_primary && <span className="badge bg-success position-absolute top-0 start-0 m-1">Principale</span>}
                <div className="mt-2 d-flex gap-1">
                  <Button size="sm" variant="outline-primary" onClick={() => openEditModal(img)} disabled={disabled}>
                    Éditer
                  </Button>
                  {!img.is_primary && (
                    <Button size="sm" variant="outline-success" onClick={() => handleSetPrimary(img.id)} disabled={disabled}>
                      Définir principale
                    </Button>
                  )}
                  <Button size="sm" variant="outline-danger" onClick={() => handleDelete(img.id)} disabled={disabled}>
                    Supprimer
                  </Button>
                </div>
                <div className="mt-1 small text-muted">{img.alt_text}</div>
              </div>
            </Col>
          ))}
        </Row>
      )}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Éditer l'image</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Form.Group className="mb-2">
              <Form.Label>Texte alternatif (alt)</Form.Label>
              <Form.Control name="alt_text" value={editForm.alt_text} onChange={handleEditChange} />
            </Form.Group>
            <Form.Group className="mb-2">
              <Form.Label>Titre</Form.Label>
              <Form.Control name="title" value={editForm.title} onChange={handleEditChange} />
            </Form.Group>
            <Button type="submit" variant="primary">
              Enregistrer
            </Button>
          </Form>
        </Modal.Body>
      </Modal>
    </div>
  );
}
