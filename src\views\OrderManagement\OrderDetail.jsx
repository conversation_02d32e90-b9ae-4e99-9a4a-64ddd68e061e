import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, <PERSON>ton, Badge, Alert, Spinner, Table, Tabs, Tab, Form, ListGroup } from 'react-bootstrap';
import {
  FaArrowLeft,
  FaEdit,
  FaFileInvoice,
  FaBoxOpen,
  FaHistory,
  FaCommentAlt,
  FaCheck,
  FaTimes,
  FaShippingFast,
  FaCreditCard,
  FaUser,
  FaMapMarkerAlt,
  FaEnvelope,
  FaPhone
} from 'react-icons/fa';
import { fetchOrderById, updateOrderStatus, fetchOrderStatuses, fetchOrderHistory } from '../../services/orderService';

const OrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // State
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('details');
  const [orderStatuses, setOrderStatuses] = useState([]);
  const [orderHistory, setOrderHistory] = useState([]);
  const [statusForm, setStatusForm] = useState({
    status: '',
    notes: ''
  });
  const [submitting, setSubmitting] = useState(false);

  // Load order data
  const loadOrder = async () => {
    setLoading(true);
    setError('');
    try {
      console.log('🔍 Loading order with ID:', id);
      const data = await fetchOrderById(id);
      console.log('✅ Order data loaded:', data);
      setOrder(data);
      setStatusForm({
        status: data.status,
        notes: ''
      });
    } catch (e) {
      console.error('❌ Error loading order:', e);
      setError(`Erreur lors du chargement de la commande: ${e.message}`);
    }
    setLoading(false);
  };

  // Load order statuses
  const loadOrderStatuses = async () => {
    try {
      const data = await fetchOrderStatuses();
      setOrderStatuses(data);
    } catch (e) {
      console.error('Erreur lors du chargement des statuts de commande:', e);
    }
  };

  // Load order history
  const loadOrderHistory = async () => {
    try {
      const data = await fetchOrderHistory(id);
      setOrderHistory(data);
    } catch (e) {
      console.error("Erreur lors du chargement de l'historique:", e);
    }
  };

  // Initial data load
  useEffect(() => {
    console.log('🔄 OrderDetail component mounted with ID:', id);
    if (id) {
      loadOrder();
      loadOrderStatuses();
      loadOrderHistory();
    } else {
      console.error('❌ No order ID provided');
      setError('Aucun ID de commande fourni');
    }
  }, [id]);

  // Handle status update
  const handleStatusUpdate = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');
    setSuccess('');

    try {
      await updateOrderStatus(id, statusForm.status, statusForm.notes);
      setSuccess('Statut mis à jour avec succès');
      loadOrder();
      loadOrderHistory();
    } catch (e) {
      setError(`Erreur lors de la mise à jour du statut: ${e.message}`);
    }

    setSubmitting(false);
  };

  // Get status badge variant
  const getStatusVariant = (status) => {
    const statusColorMap = {
      en_attente: 'warning',
      confirmee: 'info',
      en_preparation: 'primary',
      expediee: 'primary',
      livree: 'success',
      annulee: 'danger',
      remboursee: 'secondary',
      retournee: 'secondary'
    };

    return statusColorMap[status] || 'secondary';
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  if (loading) {
    return (
      <Container className="py-4 text-center">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">Chargement des détails de la commande...</p>
      </Container>
    );
  }

  if (error && !order) {
    return (
      <Container className="py-4">
        <Alert variant="danger">{error}</Alert>
        <Button variant="outline-primary" onClick={() => navigate('/orders')}>
          <FaArrowLeft className="me-2" />
          Retour aux commandes
        </Button>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">Commande #{order.numero_commande || order.order_number || `CMD-${order.id}`}</h2>
          <p className="text-muted mb-0">Détails de la commande</p>
        </div>
        <div>
          <Button variant="outline-secondary" className="me-2" onClick={() => navigate('/app/orders')}>
            <FaArrowLeft className="me-2" />
            Retour
          </Button>
          <Button variant="primary" onClick={() => window.print()}>
            <FaFileInvoice className="me-2" />
            Imprimer
          </Button>
        </div>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Main Content */}
      <Row>
        {/* Order Details */}
        <Col lg={8}>
          <Card className="shadow-sm mb-4">
            <Card.Body>
              <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-4">
                <Tab eventKey="details" title="Détails">
                  <Row>
                    <Col md={6}>
                      <h5 className="mb-3">Informations de la commande</h5>
                      <ListGroup variant="flush">
                        <ListGroup.Item>
                          <div className="d-flex justify-content-between">
                            <span className="text-muted">Numéro de commande</span>
                            <span className="fw-medium">{order.numero_commande || order.order_number || `CMD-${order.id}`}</span>
                          </div>
                        </ListGroup.Item>
                        <ListGroup.Item>
                          <div className="d-flex justify-content-between">
                            <span className="text-muted">Date de commande</span>
                            <span>{formatDate(order.created_at)}</span>
                          </div>
                        </ListGroup.Item>
                        <ListGroup.Item>
                          <div className="d-flex justify-content-between">
                            <span className="text-muted">Statut</span>
                            <Badge bg={getStatusVariant(order._original?.status || order.status)}>
                              {order._original?.status || order.status}
                            </Badge>
                          </div>
                        </ListGroup.Item>
                        <ListGroup.Item>
                          <div className="d-flex justify-content-between">
                            <span className="text-muted">Méthode de paiement</span>
                            <span>{order.methode_paiement || order.payment_method || 'Non spécifiée'}</span>
                          </div>
                        </ListGroup.Item>
                        <ListGroup.Item>
                          <div className="d-flex justify-content-between">
                            <span className="text-muted">Total</span>
                            <span className="fw-bold">{formatPrice(order.total_commande || order.total || 0)}</span>
                          </div>
                        </ListGroup.Item>
                      </ListGroup>
                    </Col>
                    <Col md={6}>
                      <h5 className="mb-3">Adresse de livraison</h5>
                      <ListGroup variant="flush">
                        <ListGroup.Item>
                          <div className="d-flex">
                            <FaMapMarkerAlt className="me-2 mt-1" />
                            <div>
                              <div>{order.shipping_address?.street || order.shipping_address_line1 || 'Non spécifiée'}</div>
                              <div>
                                {order.shipping_address?.postal_code || order.shipping_postal_code || ''}{' '}
                                {order.shipping_address?.city || order.shipping_city || ''}
                              </div>
                              <div>{order.shipping_address?.country || order.shipping_country || 'Tunisie'}</div>
                            </div>
                          </div>
                        </ListGroup.Item>
                      </ListGroup>

                      <h5 className="mb-3 mt-4">Adresse de facturation</h5>
                      <ListGroup variant="flush">
                        <ListGroup.Item>
                          <div className="d-flex">
                            <FaMapMarkerAlt className="me-2 mt-1" />
                            <div>
                              <div>
                                {order.billing_address?.street || order.shipping_address_line1 || "Identique à l'adresse de livraison"}
                              </div>
                              <div>
                                {order.billing_address?.postal_code || order.shipping_postal_code || ''}{' '}
                                {order.billing_address?.city || order.shipping_city || ''}
                              </div>
                              <div>{order.billing_address?.country || order.shipping_country || 'Tunisie'}</div>
                            </div>
                          </div>
                        </ListGroup.Item>
                      </ListGroup>
                    </Col>
                  </Row>

                  <h5 className="mb-3 mt-4">Produits commandés</h5>
                  <Table responsive>
                    <thead>
                      <tr>
                        <th>Produit</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(order.produits || order.items || []).map((produit, index) => (
                        <tr key={produit.id || index}>
                          <td>{produit.nom_produit || produit.product_name || 'Produit'}</td>
                          <td>{produit.pivot?.quantite || produit.quantity || 1}</td>
                          <td>{formatPrice(produit.pivot?.prix_unitaire || produit.unit_price || 0)}</td>
                          <td>{formatPrice(produit.pivot?.total_ligne || produit.total_price || 0)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colSpan="3" className="text-end fw-bold">
                          Total
                        </td>
                        <td className="fw-bold">{formatPrice(order.total_commande || order.total || 0)}</td>
                      </tr>
                    </tfoot>
                  </Table>
                </Tab>

                <Tab eventKey="history" title="Historique">
                  <ListGroup variant="flush">
                    {orderHistory.map((history) => (
                      <ListGroup.Item key={history.id}>
                        <div className="d-flex justify-content-between align-items-center">
                          <div>
                            <Badge bg={getStatusVariant(history.status)} className="me-2">
                              {history.status}
                            </Badge>
                            <span>{history.description}</span>
                            {history.notes && <div className="text-muted small mt-1">{history.notes}</div>}
                          </div>
                          <div className="text-muted small">{formatDate(history.created_at)}</div>
                        </div>
                      </ListGroup.Item>
                    ))}
                  </ListGroup>
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>
        </Col>

        {/* Status Update */}
        <Col lg={4}>
          <Card className="shadow-sm">
            <Card.Body>
              <h5 className="mb-4">Mettre à jour le statut</h5>
              <Form onSubmit={handleStatusUpdate}>
                <Form.Group className="mb-3">
                  <Form.Label>Nouveau statut</Form.Label>
                  <Form.Select
                    value={statusForm.status}
                    onChange={(e) => setStatusForm({ ...statusForm, status: e.target.value })}
                    required
                  >
                    <option value="">Sélectionner un statut</option>
                    {orderStatuses.map((status) => (
                      <option key={status.id} value={status.value}>
                        {status.name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Notes</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    value={statusForm.notes}
                    onChange={(e) => setStatusForm({ ...statusForm, notes: e.target.value })}
                    placeholder="Ajouter des notes sur la mise à jour du statut..."
                  />
                </Form.Group>

                <Button variant="primary" type="submit" className="w-100" disabled={submitting || !statusForm.status}>
                  {submitting ? (
                    <>
                      <Spinner animation="border" size="sm" className="me-2" />
                      Mise à jour...
                    </>
                  ) : (
                    'Mettre à jour le statut'
                  )}
                </Button>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default OrderDetail;
