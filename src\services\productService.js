const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Products (Produits)
export async function fetchProducts(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.search) queryParams.append('search', params.search);
    if (params.category_id) queryParams.append('category_id', params.category_id);
    if (params.marque_id) queryParams.append('marque_id', params.marque_id);

    const url = `${API_URL}/produits${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('Fetching products from:', url);

    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Failed to fetch products. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log('API returned products response:', response);

    return response;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

export async function fetchProductById(id) {
  try {
    const res = await fetch(`${API_URL}/produits/${id}`);
    if (!res.ok) {
      throw new Error(`Failed to fetch product ${id}. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log(`API returned product ${id} response:`, response);

    return response;
  } catch (error) {
    console.error(`Error fetching product ${id}:`, error);
    throw error;
  }
}

export async function createProduct(data) {
  try {
    const res = await fetch(`${API_URL}/produits`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la création du produit');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
}

export async function updateProduct(id, data) {
  try {
    const res = await fetch(`${API_URL}/produits/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la mise à jour du produit');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error updating product:', error);
    throw error;
  }
}

export async function deleteProduct(id) {
  try {
    const res = await fetch(`${API_URL}/produits/${id}`, {
      method: 'DELETE'
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la suppression du produit');
    }

    return res.json();
  } catch (error) {
    console.error('Error deleting product:', error);
    throw error;
  }
}

export async function searchProducts(searchTerm) {
  try {
    const res = await fetch(`${API_URL}/produits/search?q=${encodeURIComponent(searchTerm)}`);
    if (!res.ok) {
      throw new Error('Erreur lors de la recherche de produits');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error searching products:', error);
    throw error;
  }
}

export async function fetchProductsPaginated(page = 1, perPage = 10) {
  try {
    const res = await fetch(`${API_URL}/produits/perpages?page=${page}&per_page=${perPage}`);
    if (!res.ok) {
      throw new Error('Erreur lors du chargement des produits paginés');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error fetching paginated products:', error);
    throw error;
  }
}

export async function filterProducts(filters) {
  try {
    const queryParams = new URLSearchParams();

    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        queryParams.append(key, filters[key]);
      }
    });

    const res = await fetch(`${API_URL}/produits/filtrer?${queryParams.toString()}`);
    if (!res.ok) {
      throw new Error('Erreur lors du filtrage des produits');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error filtering products:', error);
    throw error;
  }
}

// Product Promotions
export async function addPromotionToProduct(productId, promotionId) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/promotions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ promotion_id: promotionId })
    });

    if (!res.ok) {
      throw new Error('Erreur lors de l\'association de la promotion au produit');
    }

    return res.json();
  } catch (error) {
    console.error('Error adding promotion to product:', error);
    throw error;
  }
}

export async function removePromotionFromProduct(productId, promotionId) {
  try {
    const res = await fetch(`${API_URL}/produits/${productId}/promotions/${promotionId}`, {
      method: 'DELETE'
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la dissociation de la promotion du produit');
    }

    return res.json();
  } catch (error) {
    console.error('Error removing promotion from product:', error);
    throw error;
  }
}
