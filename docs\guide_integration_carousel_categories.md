# Guide d'Intégration des Carrousels et Catégories en Vedette

Ce guide explique comment intégrer les carrousels, les diapositives et les catégories en vedette dans la page d'accueil de votre site e-commerce.

## Vue d'ensemble

Pour créer une page d'accueil attrayante, vous devez intégrer :

1. **Carrousels** - Présentations défilantes en haut de la page
2. **Diapositives** - Images avec texte et boutons dans chaque carrousel
3. **Catégories en vedette** - Sections mettant en avant certaines catégories de produits

## Étape 1 : Récupérer les carrousels actifs

```javascript
// Fonction pour récupérer les carrousels actifs
async function getCarrouselsActifs() {
  try {
    const response = await axios.get('https://laravel-api.fly.dev/api/carousels/actifs');
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des carrousels:', error);
    throw error;
  }
}
```

Cette fonction vous donne la liste des carrousels actifs avec leurs informations de base :
- `id`
- `nom`
- `description`
- `ordre` (pour l'ordre d'affichage)

## Étape 2 : Récupérer les diapositives d'un carrousel

```javascript
// Fonction pour récupérer les diapositives d'un carrousel
async function getDiapositivesCarrousel(carrouselId) {
  try {
    const response = await axios.get(`https://laravel-api.fly.dev/api/carousels/${carrouselId}/slides`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des diapositives:', error);
    throw error;
  }
}
```

Cette fonction retourne un tableau de diapositives avec :
- `id`
- `titre`
- `description`
- `bouton_texte`
- `bouton_lien`
- `ordre` (pour l'ordre d'affichage)
- `primary_image_url` (URL de l'image principale)

## Étape 3 : Récupérer les catégories en vedette

```javascript
// Fonction pour récupérer les catégories en vedette
async function getCategoriesEnVedette() {
  try {
    const response = await axios.get('https://laravel-api.fly.dev/api/categories/featured');
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des catégories en vedette:', error);
    throw error;
  }
}
```

Cette fonction retourne un tableau des catégories marquées comme "en vedette" avec :
- `id`
- `nom_categorie`
- `description_categorie`
- `image_categorie` (URL de l'image)
- `featured_order` (ordre d'affichage des catégories en vedette)

## Exemple complet : Charger toutes les données pour la page d'accueil

```javascript
// Fonction pour charger toutes les données nécessaires pour la page d'accueil
async function chargerDonneesPageAccueil() {
  try {
    // 1. Récupérer les carrousels actifs
    const carrousels = await getCarrouselsActifs();
    
    // 2. Pour chaque carrousel, récupérer ses diapositives
    const carrouselsAvecDiapositives = await Promise.all(
      carrousels.map(async (carrousel) => {
        const diapositives = await getDiapositivesCarrousel(carrousel.id);
        return {
          ...carrousel,
          diapositives
        };
      })
    );
    
    // 3. Récupérer les catégories en vedette
    const categoriesEnVedette = await getCategoriesEnVedette();
    
    // 4. Retourner toutes les données
    return {
      carrousels: carrouselsAvecDiapositives,
      categoriesEnVedette
    };
  } catch (error) {
    console.error('Erreur lors du chargement des données de la page d\'accueil:', error);
    throw error;
  }
}
```

## Exemple d'implémentation d'un carrousel avec React

```jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';

// Composant pour une seule diapositive
const Diapositive = ({ diapositive }) => {
  return (
    <div 
      className="diapositive" 
      style={{ backgroundImage: `url(${diapositive.primary_image_url})` }}
    >
      <div className="contenu-diapositive">
        <h2>{diapositive.titre}</h2>
        <p>{diapositive.description}</p>
        {diapositive.bouton_texte && diapositive.bouton_lien && (
          <a href={diapositive.bouton_lien} className="bouton-diapositive">
            {diapositive.bouton_texte}
          </a>
        )}
      </div>
    </div>
  );
};

// Composant Carrousel
const Carrousel = ({ carrousel }) => {
  const [indexActif, setIndexActif] = useState(0);
  const diapositives = carrousel.diapositives || [];
  
  // Fonction pour passer à la diapositive suivante
  const diapositiveSuivante = () => {
    setIndexActif((prev) => (prev === diapositives.length - 1 ? 0 : prev + 1));
  };
  
  // Fonction pour passer à la diapositive précédente
  const diapositivePrecedente = () => {
    setIndexActif((prev) => (prev === 0 ? diapositives.length - 1 : prev - 1));
  };
  
  // Défilement automatique toutes les 5 secondes
  useEffect(() => {
    if (diapositives.length <= 1) return;
    
    const intervalle = setInterval(diapositiveSuivante, 5000);
    return () => clearInterval(intervalle);
  }, [diapositives.length]);
  
  // Si pas de diapositives, ne rien afficher
  if (diapositives.length === 0) return null;
  
  return (
    <div className="carrousel">
      {/* Afficher la diapositive active */}
      <Diapositive diapositive={diapositives[indexActif]} />
      
      {/* Boutons de navigation (seulement s'il y a plus d'une diapositive) */}
      {diapositives.length > 1 && (
        <>
          <button className="bouton-precedent" onClick={diapositivePrecedente}>
            &lt;
          </button>
          <button className="bouton-suivant" onClick={diapositiveSuivante}>
            &gt;
          </button>
          
          {/* Indicateurs de position */}
          <div className="indicateurs">
            {diapositives.map((_, index) => (
              <button
                key={index}
                className={`indicateur ${index === indexActif ? 'actif' : ''}`}
                onClick={() => setIndexActif(index)}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

// Composant pour les catégories en vedette
const CategoriesEnVedette = ({ categories }) => {
  return (
    <div className="categories-vedette">
      <h2>Catégories en vedette</h2>
      <div className="grille-categories">
        {categories.map(categorie => (
          <a 
            key={categorie.id} 
            href={`/categories/${categorie.id}`} 
            className="carte-categorie"
          >
            <img 
              src={categorie.image_categorie} 
              alt={categorie.nom_categorie} 
            />
            <h3>{categorie.nom_categorie}</h3>
            <p>{categorie.description_categorie}</p>
          </a>
        ))}
      </div>
    </div>
  );
};

// Composant principal de la page d'accueil
const PageAccueil = () => {
  const [donnees, setDonnees] = useState({
    carrousels: [],
    categoriesEnVedette: []
  });
  const [chargement, setChargement] = useState(true);
  const [erreur, setErreur] = useState(null);
  
  useEffect(() => {
    async function chargerDonnees() {
      try {
        setChargement(true);
        const resultat = await chargerDonneesPageAccueil();
        setDonnees(resultat);
        setChargement(false);
      } catch (err) {
        setErreur('Erreur lors du chargement des données');
        setChargement(false);
        console.error(err);
      }
    }
    
    chargerDonnees();
  }, []);
  
  if (chargement) return <div>Chargement en cours...</div>;
  if (erreur) return <div>{erreur}</div>;
  
  return (
    <div className="page-accueil">
      {/* Afficher les carrousels */}
      {donnees.carrousels.map(carrousel => (
        <Carrousel key={carrousel.id} carrousel={carrousel} />
      ))}
      
      {/* Afficher les catégories en vedette */}
      {donnees.categoriesEnVedette.length > 0 && (
        <CategoriesEnVedette categories={donnees.categoriesEnVedette} />
      )}
      
      {/* Autres sections de la page d'accueil... */}
    </div>
  );
};

export default PageAccueil;
```

## CSS de base pour le carrousel et les catégories en vedette

```css
/* Styles pour le carrousel */
.carrousel {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
  margin-bottom: 30px;
}

.diapositive {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
}

.contenu-diapositive {
  max-width: 600px;
  padding: 30px;
  background-color: rgba(255, 255, 255, 0.8);
  margin-left: 50px;
  border-radius: 5px;
}

.contenu-diapositive h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.contenu-diapositive p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.bouton-diapositive {
  display: inline-block;
  padding: 10px 20px;
  background-color: #4a90e2;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.bouton-diapositive:hover {
  background-color: #3a70b2;
}

.bouton-precedent,
.bouton-suivant {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bouton-precedent {
  left: 20px;
}

.bouton-suivant {
  right: 20px;
}

.bouton-precedent:hover,
.bouton-suivant:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.indicateurs {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.indicateur {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
}

.indicateur.actif {
  background-color: white;
}

/* Styles pour les catégories en vedette */
.categories-vedette {
  padding: 40px 20px;
}

.categories-vedette h2 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 2rem;
}

.grille-categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.carte-categorie {
  display: block;
  text-decoration: none;
  color: inherit;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.carte-categorie:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.carte-categorie img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.carte-categorie h3 {
  padding: 15px 15px 5px;
  font-size: 1.3rem;
}

.carte-categorie p {
  padding: 0 15px 15px;
  color: #666;
}
```

## Points importants à retenir

1. **Ordre d'affichage** : Utilisez les champs `ordre` et `featured_order` pour afficher les éléments dans le bon ordre.

2. **Images** : Utilisez `primary_image_url` pour les diapositives et `image_categorie` pour les catégories.

3. **Défilement automatique** : Implémentez un défilement automatique pour le carrousel avec un intervalle raisonnable (ex: 5 secondes).

4. **Responsive design** : Assurez-vous que votre carrousel et vos grilles de catégories s'adaptent à différentes tailles d'écran.

5. **Gestion des erreurs** : Prévoyez toujours un affichage alternatif en cas d'erreur de chargement des données.

6. **Performance** : Utilisez `Promise.all` pour charger les données en parallèle et améliorer les temps de chargement.

7. **Accessibilité** : Ajoutez des attributs `aria-label` et assurez-vous que la navigation au clavier fonctionne correctement.

## Exemple d'intégration dans une page existante

Si vous avez déjà une page d'accueil existante, vous pouvez intégrer ces éléments comme suit :

```jsx
import React, { useEffect, useState } from 'react';
import { Carrousel, CategoriesEnVedette } from './components';
import { chargerDonneesPageAccueil } from './api';

// Composant de page d'accueil existant
const PageAccueilExistante = () => {
  const [donnees, setDonnees] = useState(null);
  const [chargement, setChargement] = useState(true);
  
  useEffect(() => {
    async function chargerDonnees() {
      try {
        const resultat = await chargerDonneesPageAccueil();
        setDonnees(resultat);
        setChargement(false);
      } catch (err) {
        console.error(err);
        setChargement(false);
      }
    }
    
    chargerDonnees();
  }, []);
  
  return (
    <div className="page-accueil">
      {/* Section Hero avec carrousel */}
      <section className="section-hero">
        {chargement ? (
          <div className="chargement">Chargement du carrousel...</div>
        ) : (
          donnees?.carrousels[0] && <Carrousel carrousel={donnees.carrousels[0]} />
        )}
      </section>
      
      {/* Section des catégories en vedette */}
      <section className="section-categories">
        {chargement ? (
          <div className="chargement">Chargement des catégories...</div>
        ) : (
          donnees?.categoriesEnVedette.length > 0 && (
            <CategoriesEnVedette categories={donnees.categoriesEnVedette} />
          )
        )}
      </section>
      
      {/* Autres sections existantes de votre page d'accueil */}
      <section className="section-produits-populaires">
        {/* Votre code existant pour les produits populaires */}
      </section>
      
      <section className="section-avantages">
        {/* Votre code existant pour les avantages */}
      </section>
      
      {/* Deuxième carrousel (si disponible) */}
      <section className="section-promotions">
        {chargement ? (
          <div className="chargement">Chargement des promotions...</div>
        ) : (
          donnees?.carrousels[1] && <Carrousel carrousel={donnees.carrousels[1]} />
        )}
      </section>
    </div>
  );
};

export default PageAccueilExistante;
```

Ce guide devrait vous aider à intégrer facilement les carrousels et les catégories en vedette dans votre page d'accueil existante. Si vous avez des questions supplémentaires, n'hésitez pas à demander de l'aide.
