// Service for fetching categories, sous-categories, and sous-sous-categories

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

export async function fetchCategories() {
  const res = await fetch(`${API_URL}/categories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des catégories');
  return res.json();
}

export async function fetchSousCategories(categorie_id) {
  const res = await fetch(`${API_URL}/sousCategories?categorie_id=${categorie_id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-catégories');
  return res.json();
}

export async function fetchSousSousCategories(sous_categorie_id) {
  const res = await fetch(`${API_URL}/sous_sousCategories?sous_categorie_id=${sous_categorie_id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-sous-catégories');
  return res.json();
}

// Category CRUD
export async function fetchCategoryById(id) {
  const res = await fetch(`${API_URL}/categories/${id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la catégorie');
  return res.json();
}

export async function createCategory(data) {
  const res = await fetch(`${API_URL}/categories`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la création de la catégorie');
  return res.json();
}

export async function updateCategory(id, data) {
  const res = await fetch(`${API_URL}/categories/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour de la catégorie');
  return res.json();
}

export async function deleteCategory(id) {
  const res = await fetch(`${API_URL}/categories/${id}`, { method: 'DELETE' });
  if (!res.ok) throw new Error('Erreur lors de la suppression de la catégorie');
  return res.json();
}

// Sous-catégorie CRUD
export async function fetchAllSousCategories() {
  const res = await fetch(`${API_URL}/sousCategories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-catégories');
  return res.json();
}

export async function fetchSousCategorieById(id) {
  const res = await fetch(`${API_URL}/sousCategories/${id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la sous-catégorie');
  return res.json();
}

export async function createSousCategorie(data) {
  const res = await fetch(`${API_URL}/sousCategories`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la création de la sous-catégorie');
  return res.json();
}

export async function updateSousCategorie(id, data) {
  const res = await fetch(`${API_URL}/sousCategories/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour de la sous-catégorie');
  return res.json();
}

export async function deleteSousCategorie(id) {
  const res = await fetch(`${API_URL}/sousCategories/${id}`, { method: 'DELETE' });
  if (!res.ok) throw new Error('Erreur lors de la suppression de la sous-catégorie');
  return res.json();
}

export async function fetchSousCategoriesByCategoryId(categorie_id) {
  const res = await fetch(`${API_URL}/categories/${categorie_id}/sousCategories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-catégories de la catégorie');
  return res.json();
}

// Sous-sous-catégorie CRUD
export async function fetchAllSousSousCategories() {
  const res = await fetch(`${API_URL}/sous_sousCategories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-sous-catégories');
  return res.json();
}

export async function fetchSousSousCategorieById(id) {
  const res = await fetch(`${API_URL}/sous_sousCategories/${id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la sous-sous-catégorie');
  return res.json();
}

export async function createSousSousCategorie(data) {
  const res = await fetch(`${API_URL}/sous_sousCategories`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la création de la sous-sous-catégorie');
  return res.json();
}

export async function updateSousSousCategorie(id, data) {
  const res = await fetch(`${API_URL}/sous_sousCategories/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour de la sous-sous-catégorie');
  return res.json();
}

export async function deleteSousSousCategorie(id) {
  const res = await fetch(`${API_URL}/sous_sousCategories/${id}`, { method: 'DELETE' });
  if (!res.ok) throw new Error('Erreur lors de la suppression de la sous-sous-catégorie');
  return res.json();
}

// Featured Categories
export async function fetchFeaturedCategories() {
  const res = await fetch(`${API_URL}/categories/featured`);
  if (!res.ok) throw new Error('Erreur lors du chargement des catégories mises en avant');
  return res.json();
}

export async function setFeaturedCategory(id, data) {
  const res = await fetch(`${API_URL}/categories/${id}/featured`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour du statut de mise en avant');
  return res.json();
}

export async function reorderFeaturedCategories(data) {
  const res = await fetch(`${API_URL}/categories/featured/reorder`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la réorganisation des catégories mises en avant');
  return res.json();
}
