import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Table } from 'react-bootstrap';
import { fetchOrders, fetchOrderById } from '../../services/orderService';

const OrdersDebug = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  const testOrdersList = async () => {
    setLoading(true);
    setError('');
    setResult(null);
    
    try {
      console.log('🧪 Testing orders list API...');
      const response = await fetchOrders({ page: 1, per_page: 5 });
      
      setResult({
        type: 'orders_list',
        data: response,
        success: true,
        message: 'Orders list API test successful'
      });
    } catch (e) {
      console.error('❌ Orders list test failed:', e);
      setError(`Orders list test failed: ${e.message}`);
    }
    
    setLoading(false);
  };

  const testOrderDetail = async () => {
    setLoading(true);
    setError('');
    setResult(null);
    
    try {
      console.log('🧪 Testing order detail API...');
      // Try to get the first order from the list first
      const listResponse = await fetchOrders({ page: 1, per_page: 1 });
      
      let orderId = null;
      if (listResponse?.data?.data && listResponse.data.data.length > 0) {
        orderId = listResponse.data.data[0].id;
      } else if (listResponse?.data && Array.isArray(listResponse.data) && listResponse.data.length > 0) {
        orderId = listResponse.data[0].id;
      } else {
        // Try with a test ID
        orderId = 1;
      }
      
      console.log('🧪 Testing with order ID:', orderId);
      const orderResponse = await fetchOrderById(orderId);
      
      setResult({
        type: 'order_detail',
        data: orderResponse,
        success: true,
        message: `Order detail API test successful for order #${orderId}`
      });
    } catch (e) {
      console.error('❌ Order detail test failed:', e);
      setError(`Order detail test failed: ${e.message}`);
    }
    
    setLoading(false);
  };

  const testApiConnection = async () => {
    setLoading(true);
    setError('');
    setResult(null);
    
    try {
      console.log('🧪 Testing basic API connection...');
      const apiUrl = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';
      
      const response = await fetch(`${apiUrl}/commandes`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token') || 'no-token'}`
        }
      });
      
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }
      
      setResult({
        type: 'api_connection',
        data: {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          data: responseData
        },
        success: response.ok,
        message: `API connection test: ${response.status} ${response.statusText}`
      });
    } catch (e) {
      console.error('❌ API connection test failed:', e);
      setError(`API connection test failed: ${e.message}`);
    }
    
    setLoading(false);
  };

  return (
    <Container fluid className="py-4">
      <h2 className="mb-4">Orders API Debug</h2>
      
      <Card className="mb-4">
        <Card.Header>
          <h5>API Tests</h5>
        </Card.Header>
        <Card.Body>
          <div className="d-flex gap-2 mb-3">
            <Button 
              variant="primary" 
              onClick={testApiConnection}
              disabled={loading}
            >
              Test API Connection
            </Button>
            <Button 
              variant="secondary" 
              onClick={testOrdersList}
              disabled={loading}
            >
              Test Orders List
            </Button>
            <Button 
              variant="info" 
              onClick={testOrderDetail}
              disabled={loading}
            >
              Test Order Detail
            </Button>
          </div>
          
          {loading && (
            <div className="text-center">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2">Testing API...</p>
            </div>
          )}
          
          {error && (
            <Alert variant="danger">
              <strong>Error:</strong> {error}
            </Alert>
          )}
          
          {result && (
            <Alert variant={result.success ? 'success' : 'warning'}>
              <strong>{result.message}</strong>
              <details className="mt-2">
                <summary>Response Details</summary>
                <pre className="mt-2" style={{ fontSize: '12px', maxHeight: '400px', overflow: 'auto' }}>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </details>
            </Alert>
          )}
        </Card.Body>
      </Card>
      
      <Card>
        <Card.Header>
          <h5>Environment Info</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered size="sm">
            <tbody>
              <tr>
                <td><strong>API URL</strong></td>
                <td>{import.meta.env.VITE_REACT_APP_API_URL || 'Not set'}</td>
              </tr>
              <tr>
                <td><strong>Has Access Token</strong></td>
                <td>{localStorage.getItem('access_token') ? 'Yes' : 'No'}</td>
              </tr>
              <tr>
                <td><strong>Token Preview</strong></td>
                <td>{localStorage.getItem('access_token')?.substring(0, 20) + '...' || 'None'}</td>
              </tr>
              <tr>
                <td><strong>User Data</strong></td>
                <td>{localStorage.getItem('user_data') ? 'Yes' : 'No'}</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default OrdersDebug;
