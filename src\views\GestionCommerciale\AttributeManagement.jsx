import React, { useEffect, useState } from 'react';
import {
  fetchAttributeGroups,
  createAttributeGroup,
  updateAttributeGroup,
  deleteAttributeGroup,
  fetchAttributes,
  createAttribute,
  updateAttribute,
  deleteAttribute,
  fetchAttributeValues,
  createAttributeValue,
  updateAttributeValue,
  deleteAttributeValue
} from '../../services/attributeService';
import {
  Container,
  Row,
  Col,
  Card,
  Button,
  Form,
  Table,
  Alert,
  Spinner,
  Badge,
  Modal,
  Tabs,
  Tab,
  Accordion,
  ListGroup
} from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaLayerGroup, FaTags, FaList, FaHome } from 'react-icons/fa';

export default function AttributeManagement() {
  // State for attribute groups
  const [attributeGroups, setAttributeGroups] = useState([]);
  const [groupForm, setGroupForm] = useState({ name: '', description: '' });
  const [editingGroupId, setEditingGroupId] = useState(null);
  const [groupLoading, setGroupLoading] = useState(false);
  const [groupSubmitting, setGroupSubmitting] = useState(false);

  // State for attributes
  const [attributes, setAttributes] = useState([]);
  const [attributeForm, setAttributeForm] = useState({
    name: '',
    description: '',
    type: 'select',
    attribute_group_id: '',
    filtrable: false,
    comparable: false,
    obligatoire: false,
    sous_categories: []
  });
  const [editingAttributeId, setEditingAttributeId] = useState(null);
  const [attributeLoading, setAttributeLoading] = useState(false);
  const [attributeSubmitting, setAttributeSubmitting] = useState(false);

  // State for attribute values
  const [selectedAttributeId, setSelectedAttributeId] = useState(null);
  const [attributeValues, setAttributeValues] = useState([]);
  const [valueForm, setValueForm] = useState({ value: '', display_name: '' });
  const [editingValueId, setEditingValueId] = useState(null);
  const [valueLoading, setValueLoading] = useState(false);
  const [valueSubmitting, setValueSubmitting] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState('groups');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState({ type: '', id: null });

  // Load attribute groups
  const loadAttributeGroups = async () => {
    setGroupLoading(true);
    setError('');
    try {
      const data = await fetchAttributeGroups();
      setAttributeGroups(data);
    } catch (e) {
      setError(e.message);
    }
    setGroupLoading(false);
  };

  // Load attributes
  const loadAttributes = async (groupId = null) => {
    setAttributeLoading(true);
    setError('');
    try {
      const data = await fetchAttributes(groupId);
      setAttributes(data);
    } catch (e) {
      setError(e.message);
    }
    setAttributeLoading(false);
  };

  // Load attribute values
  const loadAttributeValues = async (attributeId) => {
    if (!attributeId) return;

    setValueLoading(true);
    setError('');
    try {
      const data = await fetchAttributeValues(attributeId);
      setAttributeValues(data);
      setSelectedAttributeId(attributeId);
    } catch (e) {
      setError(e.message);
    }
    setValueLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadAttributeGroups();
    loadAttributes();
  }, []);

  // Handle group form changes
  const handleGroupChange = (e) => {
    const { name, value } = e.target;
    setGroupForm({ ...groupForm, [name]: value });
  };

  // Handle attribute form changes
  const handleAttributeChange = (e) => {
    const { name, value } = e.target;
    setAttributeForm({ ...attributeForm, [name]: value });
  };

  // Handle value form changes
  const handleValueChange = (e) => {
    const { name, value } = e.target;
    setValueForm({ ...valueForm, [name]: value });
  };

  // Submit group form
  const handleGroupSubmit = async (e) => {
    e.preventDefault();
    setGroupSubmitting(true);
    setError('');

    try {
      if (editingGroupId) {
        await updateAttributeGroup(editingGroupId, groupForm);
        setSuccess("Groupe d'attributs mis à jour avec succès");
      } else {
        await createAttributeGroup(groupForm);
        setSuccess("Groupe d'attributs créé avec succès");
      }

      setGroupForm({ name: '', description: '' });
      setEditingGroupId(null);
      loadAttributeGroups();
    } catch (e) {
      setError(e.message);
    }

    setGroupSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Submit attribute form
  const handleAttributeSubmit = async (e) => {
    e.preventDefault();
    setAttributeSubmitting(true);
    setError('');

    try {
      if (editingAttributeId) {
        await updateAttribute(editingAttributeId, attributeForm);
        setSuccess('Attribut mis à jour avec succès');
      } else {
        await createAttribute(attributeForm);
        setSuccess('Attribut créé avec succès');
      }

      setAttributeForm({
        name: '',
        description: '',
        type: 'select',
        attribute_group_id: attributeForm.attribute_group_id
      });
      setEditingAttributeId(null);
      loadAttributes();
    } catch (e) {
      setError(e.message);
    }

    setAttributeSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Submit value form
  const handleValueSubmit = async (e) => {
    e.preventDefault();
    if (!selectedAttributeId) return;

    setValueSubmitting(true);
    setError('');

    try {
      if (editingValueId) {
        await updateAttributeValue(editingValueId, valueForm);
        setSuccess("Valeur d'attribut mise à jour avec succès");
      } else {
        await createAttributeValue(selectedAttributeId, valueForm);
        setSuccess("Valeur d'attribut créée avec succès");
      }

      setValueForm({ value: '', display_name: '' });
      setEditingValueId(null);
      loadAttributeValues(selectedAttributeId);
    } catch (e) {
      setError(e.message);
    }

    setValueSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Edit group
  const handleEditGroup = (group) => {
    setGroupForm({
      name: group.name,
      description: group.description || ''
    });
    setEditingGroupId(group.id);
  };

  // Edit attribute
  const handleEditAttribute = (attribute) => {
    setAttributeForm({
      name: attribute.name,
      description: attribute.description || '',
      type: attribute.type || 'select',
      attribute_group_id: attribute.attribute_group_id || '',
      filtrable: attribute.filtrable || false,
      comparable: attribute.comparable || false,
      obligatoire: attribute.obligatoire || false,
      sous_categories: attribute.sous_categories || []
    });
    setEditingAttributeId(attribute.id);
  };

  // Edit value
  const handleEditValue = (value) => {
    setValueForm({
      value: value.value,
      display_name: value.display_name || ''
    });
    setEditingValueId(value.id);
  };

  // Confirm delete
  const confirmDelete = (type, id) => {
    setItemToDelete({ type, id });
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    setError('');

    try {
      const { type, id } = itemToDelete;

      if (type === 'group') {
        await deleteAttributeGroup(id);
        loadAttributeGroups();
        setSuccess("Groupe d'attributs supprimé avec succès");
      } else if (type === 'attribute') {
        await deleteAttribute(id);
        loadAttributes();
        setSuccess('Attribut supprimé avec succès');
      } else if (type === 'value') {
        await deleteAttributeValue(id);
        loadAttributeValues(selectedAttributeId);
        setSuccess("Valeur d'attribut supprimée avec succès");
      }
    } catch (e) {
      setError(e.message);
    }

    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaTags className="me-2" />
          Gestion des Attributs
        </h2>
        <p className="text-muted">Gérez les attributs et leurs valeurs pour les produits.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab
              eventKey="groups"
              title={
                <span>
                  <FaLayerGroup className="me-2" />
                  Groupes d'Attributs
                </span>
              }
            />
            <Tab
              eventKey="attributes"
              title={
                <span>
                  <FaTags className="me-2" />
                  Attributs
                </span>
              }
            />
            <Tab
              eventKey="values"
              title={
                <span>
                  <FaList className="me-2" />
                  Valeurs d'Attributs
                </span>
              }
            />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Tab Content */}
      {activeTab === 'groups' && (
        <>
          {/* Group Form */}
          <Card className="mb-4 shadow-sm border-0">
            <Card.Header className="bg-white py-3">
              <h5 className="mb-0 fw-bold">
                {editingGroupId ? "Modifier un groupe d'attributs" : "Ajouter un nouveau groupe d'attributs"}
              </h5>
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleGroupSubmit}>
                <Row className="g-3">
                  <Col xs={12} md={6}>
                    <Form.Group controlId="groupName">
                      <Form.Label className="fw-medium">Nom du groupe</Form.Label>
                      <Form.Control
                        name="name"
                        value={groupForm.name}
                        onChange={handleGroupChange}
                        placeholder="Ex: Caractéristiques techniques"
                        required
                        disabled={groupSubmitting}
                        className="rounded-3 border-2"
                      />
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={6}>
                    <Form.Group controlId="groupDescription">
                      <Form.Label className="fw-medium">Description</Form.Label>
                      <Form.Control
                        name="description"
                        value={groupForm.description}
                        onChange={handleGroupChange}
                        placeholder="Description du groupe d'attributs"
                        disabled={groupSubmitting}
                        className="rounded-3 border-2"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <div className="d-flex gap-2 mt-4 justify-content-end">
                  {editingGroupId && (
                    <Button
                      variant="outline-secondary"
                      type="button"
                      onClick={() => {
                        setGroupForm({ name: '', description: '' });
                        setEditingGroupId(null);
                      }}
                      className="px-4"
                    >
                      <i className="bi bi-x-circle me-2"></i>
                      Annuler
                    </Button>
                  )}
                  <Button type="submit" variant={editingGroupId ? 'warning' : 'primary'} disabled={groupSubmitting} className="px-4">
                    {groupSubmitting ? (
                      <>
                        <Spinner size="sm" animation="border" className="me-2" />
                        Traitement...
                      </>
                    ) : editingGroupId ? (
                      <>
                        <FaPencilAlt className="me-2" />
                        Mettre à jour
                      </>
                    ) : (
                      <>
                        <FaPlus className="me-2" />
                        Ajouter
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>

          {/* Groups List */}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des groupes d'attributs</h5>
              <div className="text-muted small">
                {attributeGroups.length} groupe{attributeGroups.length !== 1 ? 's' : ''} d'attributs
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {groupLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Chargement des groupes d'attributs...</p>
                </div>
              ) : attributeGroups.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <FaLayerGroup style={{ fontSize: '3rem' }} className="text-muted" />
                  </div>
                  <p className="text-muted">Aucun groupe d'attributs trouvé.</p>
                  <p className="text-muted">Utilisez le formulaire ci-dessus pour créer un nouveau groupe d'attributs.</p>
                </div>
              ) : (
                <Table hover responsive className="align-middle mb-0">
                  <thead>
                    <tr className="bg-light">
                      <th className="ps-3" style={{ width: '60px' }}>
                        ID
                      </th>
                      <th style={{ width: '25%' }}>Nom</th>
                      <th style={{ width: '35%' }}>Description</th>
                      <th style={{ width: '15%' }}>Attributs</th>
                      <th className="text-end pe-3" style={{ width: '25%' }}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {attributeGroups.map((group) => (
                      <tr key={group.id} className="border-bottom">
                        <td className="ps-3">{group.id}</td>
                        <td>
                          <span className="fw-medium">{group.name}</span>
                        </td>
                        <td>
                          <div className="text-truncate" style={{ maxWidth: '300px' }}>
                            {group.description || <span className="text-muted fst-italic">Aucune description</span>}
                          </div>
                        </td>
                        <td>
                          {group.attributes && group.attributes.length > 0 ? (
                            <Badge bg="info">{group.attributes.length} attributs</Badge>
                          ) : (
                            <span className="text-muted">Aucun attribut</span>
                          )}
                        </td>
                        <td className="text-end pe-3">
                          <Button
                            size="sm"
                            variant="outline-primary"
                            className="me-2 rounded-pill"
                            onClick={() => handleEditGroup(group)}
                            title="Éditer le groupe"
                          >
                            <FaPencilAlt className="me-1" /> Éditer
                          </Button>
                          <Button
                            size="sm"
                            variant="outline-danger"
                            className="rounded-pill"
                            onClick={() => confirmDelete('group', group.id)}
                            title="Supprimer le groupe"
                          >
                            <FaTrashAlt className="me-1" /> Supprimer
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </>
      )}

      {activeTab === 'attributes' && (
        <>
          {/* Attribute Form */}
          <Card className="mb-4 shadow-sm border-0">
            <Card.Header className="bg-white py-3">
              <h5 className="mb-0 fw-bold">{editingAttributeId ? 'Modifier un attribut' : 'Ajouter un nouvel attribut'}</h5>
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleAttributeSubmit}>
                <Row className="g-3">
                  <Col xs={12} md={4}>
                    <Form.Group controlId="attributeName">
                      <Form.Label className="fw-medium">Nom de l'attribut</Form.Label>
                      <Form.Control
                        name="name"
                        value={attributeForm.name}
                        onChange={handleAttributeChange}
                        placeholder="Ex: Couleur"
                        required
                        disabled={attributeSubmitting}
                        className="rounded-3 border-2"
                      />
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="attributeType">
                      <Form.Label className="fw-medium">Type d'attribut</Form.Label>
                      <Form.Select
                        name="type"
                        value={attributeForm.type}
                        onChange={handleAttributeChange}
                        required
                        disabled={attributeSubmitting}
                        className="rounded-3 border-2"
                      >
                        <option value="text">Texte</option>
                        <option value="number">Nombre</option>
                        <option value="boolean">Booléen</option>
                        <option value="select">Liste de valeurs</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="attributeGroup">
                      <Form.Label className="fw-medium">Groupe d'attributs</Form.Label>
                      <Form.Select
                        name="attribute_group_id"
                        value={attributeForm.attribute_group_id}
                        onChange={handleAttributeChange}
                        disabled={attributeSubmitting || attributeGroups.length === 0}
                        className="rounded-3 border-2"
                      >
                        <option value="">Aucun groupe</option>
                        {attributeGroups.map((group) => (
                          <option key={group.id} value={group.id}>
                            {group.name}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col xs={12}>
                    <Form.Group controlId="attributeDescription">
                      <Form.Label className="fw-medium">Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="description"
                        value={attributeForm.description}
                        onChange={handleAttributeChange}
                        placeholder="Description de l'attribut"
                        disabled={attributeSubmitting}
                        className="rounded-3 border-2"
                      />
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="attributeFiltrable">
                      <Form.Check
                        type="checkbox"
                        label="Filtrable"
                        name="filtrable"
                        checked={attributeForm.filtrable}
                        onChange={(e) => setAttributeForm({ ...attributeForm, filtrable: e.target.checked })}
                        disabled={attributeSubmitting}
                      />
                      <Form.Text className="text-muted">Permet de filtrer les produits par cet attribut</Form.Text>
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="attributeComparable">
                      <Form.Check
                        type="checkbox"
                        label="Comparable"
                        name="comparable"
                        checked={attributeForm.comparable}
                        onChange={(e) => setAttributeForm({ ...attributeForm, comparable: e.target.checked })}
                        disabled={attributeSubmitting}
                      />
                      <Form.Text className="text-muted">Permet de comparer les produits par cet attribut</Form.Text>
                    </Form.Group>
                  </Col>
                  <Col xs={12} md={4}>
                    <Form.Group controlId="attributeObligatoire">
                      <Form.Check
                        type="checkbox"
                        label="Obligatoire"
                        name="obligatoire"
                        checked={attributeForm.obligatoire}
                        onChange={(e) => setAttributeForm({ ...attributeForm, obligatoire: e.target.checked })}
                        disabled={attributeSubmitting}
                      />
                      <Form.Text className="text-muted">Rend cet attribut obligatoire pour les produits</Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
                <div className="d-flex gap-2 mt-4 justify-content-end">
                  {editingAttributeId && (
                    <Button
                      variant="outline-secondary"
                      type="button"
                      onClick={() => {
                        setAttributeForm({
                          name: '',
                          description: '',
                          type: 'select',
                          attribute_group_id: '',
                          filtrable: false,
                          comparable: false,
                          obligatoire: false,
                          sous_categories: []
                        });
                        setEditingAttributeId(null);
                      }}
                      className="px-4"
                    >
                      <i className="bi bi-x-circle me-2"></i>
                      Annuler
                    </Button>
                  )}
                  <Button
                    type="submit"
                    variant={editingAttributeId ? 'warning' : 'primary'}
                    disabled={attributeSubmitting}
                    className="px-4"
                  >
                    {attributeSubmitting ? (
                      <>
                        <Spinner size="sm" animation="border" className="me-2" />
                        Traitement...
                      </>
                    ) : editingAttributeId ? (
                      <>
                        <FaPencilAlt className="me-2" />
                        Mettre à jour
                      </>
                    ) : (
                      <>
                        <FaPlus className="me-2" />
                        Ajouter
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>

          {/* Attributes List */}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des attributs</h5>
              <div className="text-muted small">
                {attributes.length} attribut{attributes.length !== 1 ? 's' : ''}
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {attributeLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Chargement des attributs...</p>
                </div>
              ) : attributes.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <FaTags style={{ fontSize: '3rem' }} className="text-muted" />
                  </div>
                  <p className="text-muted">Aucun attribut trouvé.</p>
                  <p className="text-muted">Utilisez le formulaire ci-dessus pour créer un nouvel attribut.</p>
                </div>
              ) : (
                <Table hover responsive className="align-middle mb-0">
                  <thead>
                    <tr className="bg-light">
                      <th className="ps-3" style={{ width: '50px' }}>
                        ID
                      </th>
                      <th style={{ width: '20%' }}>Nom</th>
                      <th style={{ width: '10%' }}>Type</th>
                      <th style={{ width: '15%' }}>Groupe</th>
                      <th style={{ width: '20%' }}>Propriétés</th>
                      <th className="text-end pe-3" style={{ width: '25%' }}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {attributes.map((attribute) => {
                      const group = attributeGroups.find((g) => g.id === attribute.attribute_group_id);
                      return (
                        <tr key={attribute.id} className="border-bottom">
                          <td className="ps-3">{attribute.id}</td>
                          <td>
                            <span className="fw-medium">{attribute.name}</span>
                          </td>
                          <td>
                            <Badge bg="info" className="text-white">
                              {attribute.type === 'text' && 'Texte'}
                              {attribute.type === 'number' && 'Nombre'}
                              {attribute.type === 'boolean' && 'Booléen'}
                              {attribute.type === 'select' && 'Liste'}
                              {!['text', 'number', 'boolean', 'select'].includes(attribute.type) && attribute.type}
                            </Badge>
                          </td>
                          <td>
                            {group ? (
                              <Badge bg="primary" className="bg-opacity-10 text-primary">
                                {group.name}
                              </Badge>
                            ) : (
                              <span className="text-muted fst-italic">Aucun groupe</span>
                            )}
                          </td>
                          <td>
                            <div className="d-flex gap-1 flex-wrap">
                              {attribute.filtrable && (
                                <Badge bg="info" className="me-1">
                                  Filtrable
                                </Badge>
                              )}
                              {attribute.comparable && (
                                <Badge bg="success" className="me-1">
                                  Comparable
                                </Badge>
                              )}
                              {attribute.obligatoire && (
                                <Badge bg="warning" className="me-1">
                                  Obligatoire
                                </Badge>
                              )}
                              {!attribute.filtrable && !attribute.comparable && !attribute.obligatoire && (
                                <span className="text-muted fst-italic">Aucune</span>
                              )}
                            </div>
                          </td>
                          <td className="text-end pe-3">
                            <Button
                              size="sm"
                              variant="outline-info"
                              className="me-2 rounded-pill"
                              onClick={() => {
                                setSelectedAttributeId(attribute.id);
                                loadAttributeValues(attribute.id);
                                setActiveTab('values');
                              }}
                              title="Gérer les valeurs"
                            >
                              <FaList className="me-1" /> Valeurs
                            </Button>
                            <Button
                              size="sm"
                              variant="outline-primary"
                              className="me-2 rounded-pill"
                              onClick={() => handleEditAttribute(attribute)}
                              title="Éditer l'attribut"
                            >
                              <FaPencilAlt className="me-1" /> Éditer
                            </Button>
                            <Button
                              size="sm"
                              variant="outline-danger"
                              className="rounded-pill"
                              onClick={() => confirmDelete('attribute', attribute.id)}
                              title="Supprimer l'attribut"
                            >
                              <FaTrashAlt className="me-1" /> Supprimer
                            </Button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </>
      )}

      {activeTab === 'values' && (
        <>
          {!selectedAttributeId ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <FaList style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">Veuillez sélectionner un attribut pour gérer ses valeurs.</p>
              <Button variant="primary" onClick={() => setActiveTab('attributes')}>
                Sélectionner un attribut
              </Button>
            </div>
          ) : (
            <>
              {/* Selected Attribute Info */}
              <Card className="mb-4 shadow-sm border-0">
                <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
                  <h5 className="mb-0 fw-bold">
                    Valeurs pour l'attribut: {attributes.find((a) => a.id === selectedAttributeId)?.name || 'Attribut sélectionné'}
                  </h5>
                  <Button variant="outline-secondary" size="sm" onClick={() => setActiveTab('attributes')}>
                    Retour aux attributs
                  </Button>
                </Card.Header>
              </Card>

              {/* Value Form */}
              <Card className="mb-4 shadow-sm border-0">
                <Card.Header className="bg-white py-3">
                  <h5 className="mb-0 fw-bold">{editingValueId ? 'Modifier une valeur' : 'Ajouter une nouvelle valeur'}</h5>
                </Card.Header>
                <Card.Body>
                  <Form onSubmit={handleValueSubmit}>
                    <Row className="g-3">
                      <Col xs={12} md={6}>
                        <Form.Group controlId="valueValue">
                          <Form.Label className="fw-medium">Valeur</Form.Label>
                          <Form.Control
                            name="value"
                            value={valueForm.value}
                            onChange={handleValueChange}
                            placeholder="Ex: rouge"
                            required
                            disabled={valueSubmitting}
                            className="rounded-3 border-2"
                          />
                          <Form.Text className="text-muted">La valeur interne utilisée par le système.</Form.Text>
                        </Form.Group>
                      </Col>
                      <Col xs={12} md={6}>
                        <Form.Group controlId="valueDisplayName">
                          <Form.Label className="fw-medium">Nom d'affichage</Form.Label>
                          <Form.Control
                            name="display_name"
                            value={valueForm.display_name}
                            onChange={handleValueChange}
                            placeholder="Ex: Rouge"
                            disabled={valueSubmitting}
                            className="rounded-3 border-2"
                          />
                          <Form.Text className="text-muted">Le nom affiché aux utilisateurs. Si vide, la valeur sera utilisée.</Form.Text>
                        </Form.Group>
                      </Col>
                    </Row>
                    <div className="d-flex gap-2 mt-4 justify-content-end">
                      {editingValueId && (
                        <Button
                          variant="outline-secondary"
                          type="button"
                          onClick={() => {
                            setValueForm({ value: '', display_name: '' });
                            setEditingValueId(null);
                          }}
                          className="px-4"
                        >
                          <i className="bi bi-x-circle me-2"></i>
                          Annuler
                        </Button>
                      )}
                      <Button type="submit" variant={editingValueId ? 'warning' : 'primary'} disabled={valueSubmitting} className="px-4">
                        {valueSubmitting ? (
                          <>
                            <Spinner size="sm" animation="border" className="me-2" />
                            Traitement...
                          </>
                        ) : editingValueId ? (
                          <>
                            <FaPencilAlt className="me-2" />
                            Mettre à jour
                          </>
                        ) : (
                          <>
                            <FaPlus className="me-2" />
                            Ajouter
                          </>
                        )}
                      </Button>
                    </div>
                  </Form>
                </Card.Body>
              </Card>

              {/* Values List */}
              <Card className="border-0 shadow-sm">
                <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
                  <h5 className="mb-0 fw-bold">Liste des valeurs</h5>
                  <div className="text-muted small">
                    {attributeValues.length} valeur{attributeValues.length !== 1 ? 's' : ''}
                  </div>
                </Card.Header>
                <Card.Body className="p-0">
                  {valueLoading ? (
                    <div className="text-center py-5">
                      <Spinner animation="border" variant="primary" />
                      <p className="mt-3 text-muted">Chargement des valeurs...</p>
                    </div>
                  ) : attributeValues.length === 0 ? (
                    <div className="text-center py-5">
                      <div className="mb-3">
                        <FaList style={{ fontSize: '3rem' }} className="text-muted" />
                      </div>
                      <p className="text-muted">Aucune valeur trouvée pour cet attribut.</p>
                      <p className="text-muted">Utilisez le formulaire ci-dessus pour créer une nouvelle valeur.</p>
                    </div>
                  ) : (
                    <Table hover responsive className="align-middle mb-0">
                      <thead>
                        <tr className="bg-light">
                          <th className="ps-3" style={{ width: '60px' }}>
                            ID
                          </th>
                          <th style={{ width: '30%' }}>Valeur</th>
                          <th style={{ width: '30%' }}>Nom d'affichage</th>
                          <th className="text-end pe-3" style={{ width: '20%' }}>
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {attributeValues.map((value) => (
                          <tr key={value.id} className="border-bottom">
                            <td className="ps-3">{value.id}</td>
                            <td>
                              <span className="fw-medium">{value.value}</span>
                            </td>
                            <td>{value.display_name || <span className="text-muted fst-italic">Utilise la valeur</span>}</td>
                            <td className="text-end pe-3">
                              <Button
                                size="sm"
                                variant="outline-primary"
                                className="me-2 rounded-pill"
                                onClick={() => handleEditValue(value)}
                                title="Éditer la valeur"
                              >
                                <FaPencilAlt className="me-1" /> Éditer
                              </Button>
                              <Button
                                size="sm"
                                variant="outline-danger"
                                className="rounded-pill"
                                onClick={() => confirmDelete('value', value.id)}
                                title="Supprimer la valeur"
                              >
                                <FaTrashAlt className="me-1" /> Supprimer
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  )}
                </Card.Body>
              </Card>
            </>
          )}
        </>
      )}

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirmer la suppression</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {itemToDelete.type === 'group' &&
            "Êtes-vous sûr de vouloir supprimer ce groupe d'attributs ? Cette action supprimera également tous les attributs associés à ce groupe."}
          {itemToDelete.type === 'attribute' &&
            'Êtes-vous sûr de vouloir supprimer cet attribut ? Cette action supprimera également toutes les valeurs associées à cet attribut.'}
          {itemToDelete.type === 'value' && "Êtes-vous sûr de vouloir supprimer cette valeur d'attribut ?"}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDelete}>
            Supprimer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Custom CSS for styling */}
      <style jsx="true">{`
        .nav-tabs-custom .nav-link {
          color: #495057;
          font-weight: 500;
          padding: 1rem 1.5rem;
          border-radius: 0;
          border: none;
          border-bottom: 3px solid transparent;
        }
        .nav-tabs-custom .nav-link.active {
          color: #2196f3;
          background: transparent;
          border-bottom: 3px solid #2196f3;
        }
        .nav-tabs-custom .nav-link:hover:not(.active) {
          border-bottom: 3px solid #e9ecef;
        }
      `}</style>
    </Container>
  );
}
