// assets
import { IconClipboardList, IconList, IconTags, IconFile } from '@tabler/icons-react';

// constant
const icons = {
  IconClipboardList,
  IconList,
  IconTags,
  IconFile
};

// ==============================|| ORDER MANAGEMENT MENU ITEMS ||============================== //

const orderManagement = {
  id: 'order-management',
  title: 'Gestion des Commandes',
  type: 'group',
  children: [
    {
      id: 'orders',
      title: 'Commandes',
      type: 'collapse',
      icon: icons.IconClipboardList,
      children: [
        {
          id: 'orders-data',
          title: 'Données des Commandes',
          type: 'item',
          url: '/app/color',
          breadcrumbs: false
        },
        {
          id: 'order-list',
          title: 'Liste des Commandes',
          type: 'item',
          url: '/app/orders/list',
          breadcrumbs: false
        },
        {
          id: 'order-statuses',
          title: 'Statuts des Commandes',
          type: 'item',
          url: '/app/order-statuses',
          breadcrumbs: false
        }
      ]
    },
    {
      id: 'invoices',
      title: 'Factures',
      type: 'item',
      url: '/app/invoices',
      icon: icons.IconFile,
      breadcrumbs: false
    }
  ]
};

export default orderManagement;
